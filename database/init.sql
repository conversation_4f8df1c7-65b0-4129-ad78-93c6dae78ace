-- =============================================
-- 智能试衣镜数据库初始化脚本
-- 版本: v1.0
-- 创建时间: 2024-01-01
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS virtual_tryon_db
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE virtual_tryon_db;

-- =============================================
-- 1. 用户相关表
-- =============================================

-- 用户基本信息表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    uuid VARCHAR(36) UNIQUE NOT NULL COMMENT 'UUID标识',
    username VARCHAR(50) UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) COMMENT '密码哈希',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    
    -- 个人信息
    real_name VARCHAR(100) COMMENT '真实姓名',
    gender ENUM('male', 'female', 'other') COMMENT '性别',
    birthday DATE COMMENT '生日',
    height DECIMAL(5,2) COMMENT '身高(cm)',
    weight DECIMAL(5,2) COMMENT '体重(kg)',
    
    -- 身体测量数据
    bust DECIMAL(5,2) COMMENT '胸围(cm)',
    waist DECIMAL(5,2) COMMENT '腰围(cm)',
    hips DECIMAL(5,2) COMMENT '臀围(cm)',
    shoulder_width DECIMAL(5,2) COMMENT '肩宽(cm)',
    
    -- 偏好设置
    preferred_language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '偏好语言',
    preferred_currency VARCHAR(10) DEFAULT 'CNY' COMMENT '偏好货币',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    
    -- 状态字段
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '用户状态',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否验证',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_uuid (uuid),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT='用户基本信息表';

-- 用户偏好设置表
CREATE TABLE user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 服装偏好
    preferred_styles JSON COMMENT '偏好风格',
    preferred_colors JSON COMMENT '偏好颜色',
    preferred_brands JSON COMMENT '偏好品牌',
    preferred_price_range JSON COMMENT '价格区间',
    
    -- 尺码偏好
    top_size VARCHAR(10) COMMENT '上衣尺码',
    bottom_size VARCHAR(10) COMMENT '下装尺码',
    shoe_size VARCHAR(10) COMMENT '鞋码',
    
    -- 场合偏好
    occasions JSON COMMENT '适用场合',
    
    -- 风格评分 (AI分析结果)
    style_scores JSON COMMENT '风格评分',
    
    -- 设置
    enable_notifications BOOLEAN DEFAULT TRUE COMMENT '是否启用通知',
    enable_recommendations BOOLEAN DEFAULT TRUE COMMENT '是否启用推荐',
    privacy_level ENUM('public', 'friends', 'private') DEFAULT 'private' COMMENT '隐私级别',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_id (user_id)
) COMMENT='用户偏好设置表';

-- =============================================
-- 2. 服装相关表
-- =============================================

-- 服装分类表
CREATE TABLE clothing_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '分类代码',
    name_zh VARCHAR(100) NOT NULL COMMENT '中文名称',
    name_en VARCHAR(100) NOT NULL COMMENT '英文名称',
    parent_id INT COMMENT '父分类ID',
    level INT DEFAULT 1 COMMENT '分类层级',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    icon_url VARCHAR(500) COMMENT '分类图标',
    description TEXT COMMENT '分类描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES clothing_categories(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_code (code),
    INDEX idx_level (level)
) COMMENT='服装分类表';

-- 品牌表
CREATE TABLE brands (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '品牌代码',
    name VARCHAR(100) NOT NULL COMMENT '品牌名称',
    name_en VARCHAR(100) COMMENT '英文名称',
    logo_url VARCHAR(500) COMMENT '品牌Logo',
    website VARCHAR(200) COMMENT '官网地址',
    description TEXT COMMENT '品牌描述',
    country VARCHAR(50) COMMENT '品牌国家',
    established_year INT COMMENT '成立年份',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (code),
    INDEX idx_name (name)
) COMMENT='服装品牌表';

-- 服装商品主表
CREATE TABLE clothing_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sku VARCHAR(100) UNIQUE NOT NULL COMMENT '商品SKU',
    name VARCHAR(200) NOT NULL COMMENT '服装名称',
    name_en VARCHAR(200) COMMENT '英文名称',
    description TEXT COMMENT '详细描述',
    
    -- 分类和品牌
    category_id INT NOT NULL COMMENT '分类ID',
    brand_id INT COMMENT '品牌ID',
    
    -- 基本属性
    gender ENUM('male', 'female', 'unisex') NOT NULL COMMENT '适用性别',
    season ENUM('spring', 'summer', 'autumn', 'winter', 'all') DEFAULT 'all' COMMENT '适用季节',
    
    -- 价格信息
    original_price DECIMAL(10,2) COMMENT '原价',
    sale_price DECIMAL(10,2) COMMENT '售价',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币单位',
    
    -- 尺码信息
    available_sizes JSON COMMENT '可用尺码',
    size_chart JSON COMMENT '尺码对照表',
    
    -- 颜色信息
    colors JSON COMMENT '可用颜色',
    
    -- 风格标签
    styles JSON COMMENT '风格标签',
    occasions JSON COMMENT '适用场合',
    
    -- 材质信息
    materials JSON COMMENT '材质信息',
    care_instructions TEXT COMMENT '护理说明',
    
    -- 图片信息
    main_image_url VARCHAR(500) NOT NULL COMMENT '主图URL',
    
    -- 状态和权重
    status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active' COMMENT '状态',
    popularity_score DECIMAL(5,2) DEFAULT 0 COMMENT '受欢迎程度评分',
    ai_score DECIMAL(5,2) DEFAULT 0 COMMENT 'AI推荐评分',
    
    -- SEO信息
    seo_title VARCHAR(200) COMMENT 'SEO标题',
    seo_keywords VARCHAR(500) COMMENT 'SEO关键词',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES clothing_categories(id),
    FOREIGN KEY (brand_id) REFERENCES brands(id),
    
    INDEX idx_sku (sku),
    INDEX idx_category_id (category_id),
    INDEX idx_brand_id (brand_id),
    INDEX idx_gender (gender),
    INDEX idx_status (status),
    INDEX idx_popularity_score (popularity_score),
    INDEX idx_created_at (created_at),
    
    FULLTEXT INDEX ft_name_description (name, description)
) COMMENT='服装商品主表';

-- 服装图片表
CREATE TABLE clothing_images (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    clothing_id BIGINT NOT NULL COMMENT '服装ID',
    image_url VARCHAR(500) NOT NULL COMMENT '图片URL',
    image_type ENUM('main', 'detail', 'model', 'thumbnail') NOT NULL COMMENT '图片类型',
    color_variant VARCHAR(50) COMMENT '对应颜色变体',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    alt_text VARCHAR(200) COMMENT '图片Alt文本',
    width INT COMMENT '图片宽度',
    height INT COMMENT '图片高度',
    file_size INT COMMENT '文件大小(字节)',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (clothing_id) REFERENCES clothing_items(id) ON DELETE CASCADE,
    INDEX idx_clothing_id (clothing_id),
    INDEX idx_image_type (image_type),
    INDEX idx_sort_order (sort_order)
) COMMENT='服装图片表';

-- =============================================
-- 3. 试衣相关表
-- =============================================

-- 试衣历史记录表
CREATE TABLE tryon_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    uuid VARCHAR(36) UNIQUE NOT NULL COMMENT 'UUID标识',
    user_id BIGINT COMMENT '用户ID (可为空，支持游客)',
    session_id VARCHAR(100) COMMENT '会话ID (游客标识)',
    
    -- 试衣信息
    user_photo_url VARCHAR(500) NOT NULL COMMENT '用户照片URL',
    clothing_id BIGINT NOT NULL COMMENT '服装ID',
    result_image_url VARCHAR(500) COMMENT '试衣结果图URL',
    
    -- AI处理信息
    ai_task_id VARCHAR(100) COMMENT 'AI任务ID',
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '处理状态',
    processing_time INT COMMENT '处理耗时(秒)',
    ai_confidence DECIMAL(5,4) COMMENT 'AI置信度',
    
    -- 质量评估
    quality_score DECIMAL(5,2) COMMENT '结果质量评分',
    user_rating TINYINT COMMENT '用户评分 1-5',
    user_feedback TEXT COMMENT '用户反馈',
    
    -- 参数配置
    try_on_options JSON COMMENT '试衣参数配置',
    
    -- 设备信息
    device_info JSON COMMENT '设备信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    
    -- 状态
    is_favorite BOOLEAN DEFAULT FALSE COMMENT '是否收藏',
    is_shared BOOLEAN DEFAULT FALSE COMMENT '是否分享',
    visibility ENUM('private', 'public', 'friends') DEFAULT 'private' COMMENT '可见性',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (clothing_id) REFERENCES clothing_items(id),
    
    INDEX idx_uuid (uuid),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_clothing_id (clothing_id),
    INDEX idx_status (processing_status),
    INDEX idx_created_at (created_at),
    INDEX idx_is_favorite (is_favorite)
) COMMENT='试衣历史记录表';

-- AI推荐记录表
CREATE TABLE ai_recommendations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    session_id VARCHAR(100) COMMENT '会话ID',
    
    -- 推荐信息
    recommendation_type ENUM('style_based', 'history_based', 'collaborative', 'trending') NOT NULL COMMENT '推荐类型',
    clothing_ids JSON NOT NULL COMMENT '推荐的服装ID列表',
    scores JSON COMMENT '推荐评分',
    reasons JSON COMMENT '推荐理由',
    
    -- 用户反馈
    clicked_items JSON COMMENT '用户点击的商品',
    conversion_items JSON COMMENT '用户试穿的商品',
    feedback_score DECIMAL(5,2) COMMENT '反馈评分',
    
    -- 算法信息
    algorithm_version VARCHAR(20) COMMENT '算法版本',
    model_name VARCHAR(50) COMMENT '模型名称',
    confidence_scores JSON COMMENT '置信度评分',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_recommendation_type (recommendation_type),
    INDEX idx_created_at (created_at)
) COMMENT='AI推荐记录表';

-- =============================================
-- 4. 社交和分享表
-- =============================================

-- 用户关注关系表
CREATE TABLE user_follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_id BIGINT NOT NULL COMMENT '关注者ID',
    followed_id BIGINT NOT NULL COMMENT '被关注者ID',
    status ENUM('active', 'blocked') DEFAULT 'active' COMMENT '关注状态',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (followed_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY uk_follow (follower_id, followed_id),
    INDEX idx_follower_id (follower_id),
    INDEX idx_followed_id (followed_id)
) COMMENT='用户关注关系表';

-- 分享记录表
CREATE TABLE share_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '分享用户ID',
    tryon_id BIGINT NOT NULL COMMENT '试衣记录ID',
    
    share_platform ENUM('wechat', 'weibo', 'qq', 'douyin', 'link', 'qr_code') NOT NULL COMMENT '分享平台',
    share_url VARCHAR(500) COMMENT '分享链接',
    share_title VARCHAR(200) COMMENT '分享标题',
    share_description TEXT COMMENT '分享描述',
    
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (tryon_id) REFERENCES tryon_history(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_tryon_id (tryon_id),
    INDEX idx_platform (share_platform),
    INDEX idx_created_at (created_at)
) COMMENT='分享记录表';

-- =============================================
-- 5. 系统配置表
-- =============================================

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    category VARCHAR(50) COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(客户端可访问)',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
) COMMENT='系统配置表';

-- 错误日志表
CREATE TABLE error_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    session_id VARCHAR(100) COMMENT '会话ID',
    
    error_type VARCHAR(50) NOT NULL COMMENT '错误类型',
    error_code VARCHAR(20) COMMENT '错误代码',
    error_message TEXT NOT NULL COMMENT '错误信息',
    stack_trace TEXT COMMENT '堆栈跟踪',
    
    request_url VARCHAR(500) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params JSON COMMENT '请求参数',
    
    device_info JSON COMMENT '设备信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium' COMMENT '严重程度',
    status ENUM('new', 'investigating', 'resolved', 'ignored') DEFAULT 'new' COMMENT '处理状态',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_error_type (error_type),
    INDEX idx_severity (severity),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT='错误日志表';

-- =============================================
-- 6. 初始化数据
-- =============================================

-- 插入默认分类数据
INSERT INTO clothing_categories (code, name_zh, name_en, level, sort_order) VALUES
('tops', '上装', 'Tops', 1, 1),
('bottoms', '下装', 'Bottoms', 1, 2),
('dresses', '连衣裙', 'Dresses', 1, 3),
('outerwear', '外套', 'Outerwear', 1, 4),
('accessories', '配饰', 'Accessories', 1, 5);

-- 插入子分类
INSERT INTO clothing_categories (code, name_zh, name_en, parent_id, level, sort_order) VALUES
('t-shirts', 'T恤', 'T-Shirts', 1, 2, 1),
('shirts', '衬衫', 'Shirts', 1, 2, 2),
('sweaters', '毛衣', 'Sweaters', 1, 2, 3),
('blouses', '女式上衣', 'Blouses', 1, 2, 4),
('jeans', '牛仔裤', 'Jeans', 2, 2, 1),
('trousers', '长裤', 'Trousers', 2, 2, 2),
('shorts', '短裤', 'Shorts', 2, 2, 3),
('skirts', '裙子', 'Skirts', 2, 2, 4),
('casual-dress', '休闲连衣裙', 'Casual Dresses', 3, 2, 1),
('formal-dress', '正式连衣裙', 'Formal Dresses', 3, 2, 2),
('jackets', '夹克', 'Jackets', 4, 2, 1),
('coats', '大衣', 'Coats', 4, 2, 2),
('hats', '帽子', 'Hats', 5, 2, 1),
('bags', '包包', 'Bags', 5, 2, 2);

-- 插入默认品牌
INSERT INTO brands (code, name, name_en, country, established_year) VALUES
('uniqlo', '优衣库', 'UNIQLO', 'Japan', 1984),
('zara', 'ZARA', 'ZARA', 'Spain', 1975),
('hm', 'H&M', 'H&M', 'Sweden', 1947),
('nike', '耐克', 'Nike', 'USA', 1964),
('adidas', '阿迪达斯', 'Adidas', 'Germany', 1949),
('gap', '盖璞', 'GAP', 'USA', 1969),
('muji', '无印良品', 'MUJI', 'Japan', 1980),
('gu', 'GU', 'GU', 'Japan', 2006);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, category, description, is_public) VALUES
('ai_api_url', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/virtualtryon/text2image', 'string', 'ai', 'AI试衣API地址', FALSE),
('ai_api_key', 'your_api_key_here', 'string', 'ai', 'AI API密钥', FALSE),
('max_upload_size', '10485760', 'number', 'upload', '最大上传文件大小(字节)', TRUE),
('supported_image_formats', '["jpg", "jpeg", "png", "webp"]', 'json', 'upload', '支持的图片格式', TRUE),
('recommendation_count', '20', 'number', 'recommendation', '推荐商品数量', TRUE),
('enable_guest_tryon', 'true', 'boolean', 'feature', '是否允许游客试衣', TRUE),
('image_quality', '80', 'number', 'image', '图片压缩质量', TRUE),
('cache_duration', '3600', 'number', 'cache', '缓存时长(秒)', FALSE),
('max_history_records', '100', 'number', 'history', '用户最大历史记录数', TRUE),
('enable_social_features', 'false', 'boolean', 'social', '是否启用社交功能', TRUE);

-- 插入示例服装数据
INSERT INTO clothing_items (sku, name, name_en, description, category_id, brand_id, gender, season, 
                          original_price, sale_price, available_sizes, colors, styles, occasions, 
                          main_image_url, status, popularity_score) VALUES
('TOP001', '基础白色T恤', 'Basic White T-Shirt', '经典百搭纯棉T恤，舒适透气', 6, 1, 'unisex', 'all', 
 39.00, 29.00, '["XS", "S", "M", "L", "XL"]', 
 '[{"name": "白色", "hex": "#FFFFFF"}, {"name": "黑色", "hex": "#000000"}]',
 '["casual", "basic"]', '["daily", "sport"]',
 'https://example.com/images/basic-white-tshirt.jpg', 'active', 85.5),

('TOP002', '条纹长袖衬衫', 'Striped Long Sleeve Shirt', '时尚条纹设计，适合商务休闲', 7, 2, 'male', 'all',
 199.00, 159.00, '["S", "M", "L", "XL"]',
 '[{"name": "蓝白条纹", "hex": "#4A90E2"}, {"name": "黑白条纹", "hex": "#000000"}]',
 '["business", "casual"]', '["work", "daily"]',
 'https://example.com/images/striped-shirt.jpg', 'active', 78.3),

('BOT001', '修身牛仔裤', 'Slim Fit Jeans', '经典修身剪裁，展现完美腿型', 10, 3, 'female', 'all',
 299.00, 199.00, '["26", "27", "28", "29", "30"]',
 '[{"name": "深蓝色", "hex": "#1E3A8A"}, {"name": "黑色", "hex": "#000000"}]',
 '["casual", "trendy"]', '["daily", "date"]',
 'https://example.com/images/slim-jeans.jpg', 'active', 92.1),

('DRE001', '优雅连衣裙', 'Elegant Dress', '优雅气质连衣裙，适合正式场合', 14, 2, 'female', 'spring',
 599.00, 399.00, '["XS", "S", "M", "L"]',
 '[{"name": "黑色", "hex": "#000000"}, {"name": "深蓝", "hex": "#1E3A8A"}]',
 '["formal", "elegant"]', '["work", "party"]',
 'https://example.com/images/elegant-dress.jpg', 'active', 87.6);

-- 为服装添加额外图片
INSERT INTO clothing_images (clothing_id, image_url, image_type, color_variant, sort_order, alt_text) VALUES
(1, 'https://example.com/images/basic-white-tshirt-detail1.jpg', 'detail', 'white', 1, '白色T恤细节图1'),
(1, 'https://example.com/images/basic-white-tshirt-detail2.jpg', 'detail', 'white', 2, '白色T恤细节图2'),
(1, 'https://example.com/images/basic-black-tshirt.jpg', 'main', 'black', 1, '黑色T恤主图'),
(2, 'https://example.com/images/striped-shirt-detail.jpg', 'detail', 'blue', 1, '条纹衬衫细节图'),
(3, 'https://example.com/images/slim-jeans-back.jpg', 'detail', 'dark_blue', 1, '牛仔裤背面图'),
(4, 'https://example.com/images/elegant-dress-model.jpg', 'model', 'black', 1, '连衣裙模特图');

-- =============================================
-- 7. 视图创建
-- =============================================

-- 创建服装详情视图
CREATE VIEW clothing_details AS
SELECT 
    ci.id,
    ci.sku,
    ci.name,
    ci.description,
    cc.name_zh as category_name,
    b.name as brand_name,
    ci.gender,
    ci.season,
    ci.sale_price,
    ci.currency,
    ci.main_image_url,
    ci.popularity_score,
    ci.status,
    ci.created_at
FROM clothing_items ci
LEFT JOIN clothing_categories cc ON ci.category_id = cc.id
LEFT JOIN brands b ON ci.brand_id = b.id
WHERE ci.status = 'active';

-- 创建用户试衣统计视图
CREATE VIEW user_tryon_stats AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(th.id) as total_tryons,
    COUNT(CASE WHEN th.processing_status = 'completed' THEN 1 END) as successful_tryons,
    COUNT(CASE WHEN th.is_favorite = TRUE THEN 1 END) as favorite_count,
    AVG(th.user_rating) as avg_rating,
    MAX(th.created_at) as last_tryon_at
FROM users u
LEFT JOIN tryon_history th ON u.id = th.user_id
GROUP BY u.id, u.username;

-- =============================================
-- 8. 触发器创建
-- =============================================

-- 创建触发器：更新用户最后登录时间
DELIMITER //
CREATE TRIGGER update_last_login 
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    IF NEW.status = 'active' AND OLD.status != 'active' THEN
        SET NEW.last_login_at = CURRENT_TIMESTAMP;
    END IF;
END //
DELIMITER ;

-- 创建触发器：自动生成UUID
DELIMITER //
CREATE TRIGGER generate_user_uuid 
    BEFORE INSERT ON users
    FOR EACH ROW
BEGIN
    IF NEW.uuid IS NULL OR NEW.uuid = '' THEN
        SET NEW.uuid = UUID();
    END IF;
END //
DELIMITER ;

DELIMITER //
CREATE TRIGGER generate_tryon_uuid 
    BEFORE INSERT ON tryon_history
    FOR EACH ROW
BEGIN
    IF NEW.uuid IS NULL OR NEW.uuid = '' THEN
        SET NEW.uuid = UUID();
    END IF;
END //
DELIMITER ;

-- =============================================
-- 脚本执行完成
-- =============================================

SELECT 'Database initialization completed successfully!' as message; 