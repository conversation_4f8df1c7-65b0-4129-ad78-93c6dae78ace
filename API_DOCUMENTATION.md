# 智能试衣镜 API 文档

## 📋 概述

本文档详细描述了智能试衣镜应用的后端API接口设计，包含用户认证、服装管理、AI试衣、图片上传、推荐系统等核心功能模块。

### 🌐 基础信息

- **API基础URL**: `https://api.your-domain.com/api/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

### 🔑 通用请求头

```http
Content-Type: application/json
Authorization: Bearer {access_token}
Accept: application/json
X-Client-Version: 1.0.0
X-Platform: web|android|ios
```

### 📊 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

## 1️⃣ 用户认证模块

### 1.1 用户注册

**POST** `/auth/register`

注册新用户账号

#### 请求参数

```json
{
  "email": "<EMAIL>",
  "username": "用户名",
  "phone": "+86 13800138000",
  "password": "password123",
  "gender": "male|female|other",
  "age": 25,
  "terms_accepted": true
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "user_id": "usr_123456789",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "rt_987654321",
    "expires_at": "2024-01-08T00:00:00Z",
    "profile": {
      "username": "用户名",
      "email": "<EMAIL>",
      "avatar": null,
      "gender": "male",
      "age": 25
    }
  }
}
```

### 1.2 用户登录

**POST** `/auth/login`

用户登录获取访问令牌

#### 请求参数

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "user_id": "usr_123456789",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "rt_987654321",
    "expires_at": "2024-01-08T00:00:00Z",
    "profile": {
      "username": "用户名",
      "email": "<EMAIL>",
      "avatar": "https://cdn.example.com/avatar.jpg",
      "gender": "male",
      "age": 25
    }
  }
}
```

### 1.3 刷新Token

**POST** `/auth/refresh`

使用refresh_token刷新访问令牌

#### 请求参数

```json
{
  "refresh_token": "rt_987654321"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-01-08T00:00:00Z"
  }
}
```

---

## 2️⃣ 服装管理模块

### 2.1 获取服装列表

**GET** `/clothing`

获取服装商品列表，支持分页和筛选

#### 查询参数

| 参数 | 类型 | 必填 | 描述 | 示例 |
|------|------|------|------|------|
| page | int | 否 | 页码，默认1 | `1` |
| limit | int | 否 | 每页数量，默认20 | `20` |
| category | string | 否 | 服装分类 | `tops` |
| brand | string | 否 | 品牌名称 | `Nike` |
| color | string | 否 | 颜色 | `red` |
| style | string | 否 | 风格 | `casual` |
| min_price | float | 否 | 最低价格 | `100.00` |
| max_price | float | 否 | 最高价格 | `1000.00` |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "cloth_123",
        "name": "时尚休闲T恤",
        "brand": "Nike",
        "category": "tops",
        "price": 299.00,
        "currency": "CNY",
        "colors": ["white", "black", "blue"],
        "sizes": ["S", "M", "L", "XL"],
        "style": "casual",
        "material": "纯棉",
        "description": "舒适透气的休闲T恤",
        "images": [
          {
            "url": "https://cdn.example.com/cloth1_main.jpg",
            "type": "main",
            "alt": "主图"
          },
          {
            "url": "https://cdn.example.com/cloth1_detail.jpg",
            "type": "detail",
            "alt": "细节图"
          }
        ],
        "tags": ["休闲", "舒适", "百搭"],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 2.2 获取服装详情

**GET** `/clothing/{clothing_id}`

获取指定服装的详细信息

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| clothing_id | string | 是 | 服装ID |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "id": "cloth_123",
    "name": "时尚休闲T恤",
    "brand": "Nike",
    "category": "tops",
    "price": 299.00,
    "currency": "CNY",
    "colors": ["white", "black", "blue"],
    "sizes": ["S", "M", "L", "XL"],
    "style": "casual",
    "material": "纯棉",
    "description": "舒适透气的休闲T恤，采用优质纯棉面料制作...",
    "images": [...],
    "tags": ["休闲", "舒适", "百搭"],
    "specifications": {
      "weight": "200g",
      "thickness": "中等",
      "elasticity": "微弹",
      "care_instructions": "机洗，低温烘干"
    },
    "inventory": {
      "S": 10,
      "M": 15,
      "L": 20,
      "XL": 8
    },
    "ratings": {
      "average": 4.5,
      "count": 128
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2.3 搜索服装

**GET** `/clothing/search`

根据关键词搜索服装

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| category | string | 否 | 限定分类 |
| page | int | 否 | 页码 |
| limit | int | 否 | 每页数量 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {...},
    "search_info": {
      "keyword": "T恤",
      "total_results": 45,
      "search_time": 0.023
    }
  }
}
```

---

## 3️⃣ 文件上传模块

### 3.1 上传用户照片

**POST** `/upload/user-photo`

上传用户拍摄的照片用于AI试衣

#### 请求格式

`Content-Type: multipart/form-data`

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| image | file | 是 | 图片文件 |
| type | string | 否 | 图片类型，如 "mirror_photo" |

#### 文件要求

- **格式**: JPG, PNG, WEBP
- **大小**: 最大10MB
- **尺寸**: 最小200x200px，最大4096x4096px
- **比例**: 建议3:4或4:3

#### 响应示例

```json
{
  "success": true,
  "data": {
    "file_id": "file_123456789",
    "url": "https://cdn.example.com/user-photos/abc123.jpg",
    "file_name": "user_photo_20240101.jpg",
    "file_size": 2048576,
    "dimensions": {
      "width": 1024,
      "height": 1536
    },
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

---

## 4️⃣ AI试衣模块

### 4.1 提交试衣任务

**POST** `/tryon/submit`

提交AI试衣任务，开始生成试衣效果

#### 请求参数

```json
{
  "user_photo_url": "https://cdn.example.com/user-photos/abc123.jpg",
  "clothing_id": "cloth_123",
  "options": {
    "quality": "high",
    "pose_adjustment": true,
    "lighting_enhancement": true,
    "background_removal": false
  }
}
```

#### 选项说明

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| quality | string | "medium" | 生成质量: low/medium/high |
| pose_adjustment | boolean | true | 是否进行姿态调整 |
| lighting_enhancement | boolean | true | 是否增强光照 |
| background_removal | boolean | false | 是否移除背景 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "task_id": "task_987654321",
    "status": "pending",
    "estimated_time": 30,
    "progress": 0,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4.2 查询任务状态

**GET** `/tryon/task/{task_id}`

查询AI试衣任务的处理状态

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| task_id | string | 是 | 任务ID |

#### 响应示例

**处理中**
```json
{
  "success": true,
  "data": {
    "task_id": "task_987654321",
    "status": "processing",
    "progress": 65,
    "estimated_remaining_time": 15,
    "current_step": "生成试衣效果",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**处理完成**
```json
{
  "success": true,
  "data": {
    "task_id": "task_987654321",
    "status": "completed",
    "progress": 100,
    "result_url": "https://cdn.example.com/results/result_123.jpg",
    "thumbnail_url": "https://cdn.example.com/results/thumb_123.jpg",
    "processing_time": 28,
    "created_at": "2024-01-01T00:00:00Z",
    "completed_at": "2024-01-01T00:00:28Z"
  }
}
```

**处理失败**
```json
{
  "success": true,
  "data": {
    "task_id": "task_987654321",
    "status": "failed",
    "error": "图片质量不足，请重新上传更清晰的照片",
    "error_code": "POOR_IMAGE_QUALITY",
    "created_at": "2024-01-01T00:00:00Z",
    "failed_at": "2024-01-01T00:00:15Z"
  }
}
```

#### 状态说明

| 状态 | 描述 |
|------|------|
| pending | 任务已提交，等待处理 |
| processing | 正在处理中 |
| completed | 处理完成 |
| failed | 处理失败 |
| cancelled | 任务已取消 |

### 4.3 获取试衣历史

**GET** `/tryon/history`

获取用户的试衣历史记录

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认20 |
| status | string | 否 | 筛选状态 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "task_id": "task_987654321",
        "user_photo_url": "https://cdn.example.com/user-photos/abc123.jpg",
        "clothing": {
          "id": "cloth_123",
          "name": "时尚休闲T恤",
          "brand": "Nike",
          "images": [...]
        },
        "result_url": "https://cdn.example.com/results/result_123.jpg",
        "status": "completed",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "pages": 3
    }
  }
}
```

---

## 5️⃣ AI推荐模块

### 5.1 获取个性化推荐

**POST** `/ai/recommendations`

基于用户照片和偏好获取个性化服装推荐

#### 请求参数

```json
{
  "user_photo_url": "https://cdn.example.com/user-photos/abc123.jpg",
  "style": "casual",
  "occasion": "daily",
  "season": "spring",
  "limit": 10
}
```

#### 参数说明

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_photo_url | string | 否 | 用户照片URL |
| style | string | 否 | 偏好风格 |
| occasion | string | 否 | 使用场合 |
| season | string | 否 | 季节 |
| limit | int | 否 | 推荐数量，默认10 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "clothing": {
          "id": "cloth_456",
          "name": "经典牛仔夹克",
          "brand": "Levi's",
          "category": "jackets",
          "price": 599.00,
          "images": [...]
        },
        "score": 0.95,
        "reason": "与您的风格完美匹配，适合休闲场合",
        "tags": ["高匹配度", "热门单品", "百搭"],
        "compatibility": {
          "style_match": 0.98,
          "color_harmony": 0.92,
          "season_fit": 0.95
        }
      }
    ],
    "analysis": {
      "detected_style": "casual_modern",
      "color_preference": ["blue", "white", "gray"],
      "body_type": "athletic",
      "confidence": 0.87
    }
  }
}
```

### 5.2 分析用户风格

**POST** `/ai/style-analysis`

分析用户照片，识别穿衣风格和体型特征

#### 请求参数

```json
{
  "user_photo_url": "https://cdn.example.com/user-photos/abc123.jpg"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "primary_style": "casual_modern",
    "suitable_styles": ["casual", "business_casual", "minimalist"],
    "style_scores": {
      "casual": 0.92,
      "formal": 0.34,
      "vintage": 0.23,
      "minimalist": 0.78,
      "bohemian": 0.12
    },
    "body_analysis": {
      "body_type": "athletic",
      "height_estimate": "medium",
      "build": "average"
    },
    "color_analysis": {
      "skin_tone": "warm",
      "recommended_colors": ["navy", "cream", "earth_tones"],
      "avoid_colors": ["bright_pink", "neon_green"]
    },
    "recommendations": [
      "选择修身版型突出身材优势",
      "尝试叠穿增加层次感",
      "配饰可以选择简约风格"
    ],
    "confidence": 0.89
  }
}
```

---

## 6️⃣ 用户偏好模块

### 6.1 保存用户偏好

**POST** `/user/preferences`

保存用户的穿衣偏好和个人信息

#### 请求参数

```json
{
  "favorite_colors": ["blue", "white", "gray"],
  "favorite_styles": ["casual", "minimalist"],
  "favorite_brands": ["Nike", "Adidas", "Uniqlo"],
  "preferred_size": "M",
  "body_measurements": {
    "height": 170,
    "weight": 65,
    "chest": 90,
    "waist": 75,
    "hip": 95
  },
  "style_preferences": {
    "fit_preference": "regular",
    "length_preference": "medium",
    "pattern_preference": "solid"
  },
  "other_preferences": {
    "budget_range": "200-500",
    "shopping_frequency": "monthly",
    "preferred_occasions": ["daily", "work", "weekend"]
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "success": true,
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 6.2 获取用户偏好

**GET** `/user/preferences`

获取当前用户的偏好设置

#### 响应示例

```json
{
  "success": true,
  "data": {
    "favorite_colors": ["blue", "white", "gray"],
    "favorite_styles": ["casual", "minimalist"],
    "favorite_brands": ["Nike", "Adidas", "Uniqlo"],
    "preferred_size": "M",
    "body_measurements": {...},
    "style_preferences": {...},
    "other_preferences": {...},
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

---

## 7️⃣ 收藏和分享模块

### 7.1 收藏试衣结果

**POST** `/user/favorites`

收藏喜欢的试衣结果

#### 请求参数

```json
{
  "task_id": "task_987654321",
  "tags": ["喜欢", "想买"]
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "success": true,
    "favorite_id": "fav_123456",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 7.2 分享试衣结果

**POST** `/share`

生成分享链接，支持多平台分享

#### 请求参数

```json
{
  "task_id": "task_987654321",
  "platform": "wechat",
  "message": "看看我的新搭配！",
  "privacy_level": "public"
}
```

#### 平台支持

| 平台 | 值 | 描述 |
|------|-----|------|
| 微信 | wechat | 微信分享 |
| 微博 | weibo | 微博分享 |
| QQ | qq | QQ分享 |
| 链接 | link | 通用链接 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "share_id": "share_789012",
    "share_url": "https://app.example.com/share/share_789012",
    "platform": "wechat",
    "qr_code": "https://cdn.example.com/qr/share_789012.png",
    "expire_at": "2024-01-08T00:00:00Z"
  }
}
```

---

## 8️⃣ 系统配置模块

### 8.1 获取应用配置

**GET** `/config`

获取应用的配置信息

#### 响应示例

```json
{
  "success": true,
  "data": {
    "version": "1.0.0",
    "features": {
      "ai_tryon": true,
      "style_analysis": true,
      "recommendations": true,
      "social_sharing": true
    },
    "endpoints": {
      "upload_max_size": "10MB",
      "supported_formats": ["jpg", "png", "webp"],
      "cdn_domain": "https://cdn.example.com"
    },
    "settings": {
      "default_quality": "medium",
      "processing_timeout": 60,
      "max_history_items": 100
    }
  }
}
```

### 8.2 错误日志上报

**POST** `/system/error-report`

客户端错误日志上报

#### 请求参数

```json
{
  "error": "NetworkException: Connection timeout",
  "stack_trace": "...",
  "context": {
    "user_id": "usr_123456789",
    "screen": "tryon_processing",
    "device_info": {
      "platform": "android",
      "version": "11",
      "model": "Pixel 6"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "success": true,
    "report_id": "err_20240101_001"
  }
}
```

---

## 🚨 错误码说明

### 通用错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `METHOD_NOT_ALLOWED` | 405 | 方法不允许 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |

### 业务错误码

| 错误码 | 描述 |
|--------|------|
| `USER_NOT_FOUND` | 用户不存在 |
| `INVALID_CREDENTIALS` | 登录凭据无效 |
| `EMAIL_ALREADY_EXISTS` | 邮箱已存在 |
| `TOKEN_EXPIRED` | Token已过期 |
| `INVALID_TOKEN` | Token无效 |
| `CLOTHING_NOT_FOUND` | 服装不存在 |
| `POOR_IMAGE_QUALITY` | 图片质量不佳 |
| `UNSUPPORTED_IMAGE_FORMAT` | 不支持的图片格式 |
| `IMAGE_TOO_LARGE` | 图片过大 |
| `TASK_NOT_FOUND` | 任务不存在 |
| `TASK_FAILED` | 任务处理失败 |
| `INSUFFICIENT_CREDITS` | 积分不足 |

---

## 📈 请求限制

### 频率限制

| 接口类型 | 限制 | 说明 |
|----------|------|------|
| 认证接口 | 5次/分钟 | 单IP限制 |
| 上传接口 | 10次/分钟 | 单用户限制 |
| AI试衣接口 | 3次/分钟 | 单用户限制 |
| 查询接口 | 100次/分钟 | 单用户限制 |

### 文件限制

| 类型 | 限制 |
|------|------|
| 单文件大小 | 最大10MB |
| 图片格式 | JPG, PNG, WEBP |
| 图片尺寸 | 200x200px - 4096x4096px |

---

## 🔒 安全说明

### 1. 认证机制
- 采用JWT Token认证
- Access Token有效期7天
- Refresh Token有效期30天
- 支持Token自动刷新

### 2. 数据加密
- 所有API通信使用HTTPS
- 敏感数据使用AES-256加密存储
- 图片URL采用签名防盗链

### 3. 隐私保护
- 用户照片自动添加水印
- 支持照片过期自动删除
- 严格的数据访问权限控制

---

## 📝 开发指南

### 1. SDK使用

```dart
// Flutter SDK 使用示例
final apiService = ApiService();

// 用户登录
final loginResult = await apiService.login(
  email: '<EMAIL>',
  password: 'password123',
);

if (loginResult.success) {
  final user = loginResult.data!;
  print('登录成功: ${user.profile.username}');
}

// 获取服装列表
final clothingResult = await apiService.getClothingList(
  category: 'tops',
  page: 1,
  limit: 20,
);

if (clothingResult.success) {
  final clothingList = clothingResult.data!;
  print('获取到 ${clothingList.length} 件服装');
}

// 提交试衣任务
final tryOnResult = await apiService.submitTryOnTask(
  userPhotoUrl: 'https://example.com/user.jpg',
  clothingId: 'cloth_123',
);

if (tryOnResult.success) {
  final task = tryOnResult.data!;
  print('任务ID: ${task.taskId}');
  
  // 轮询查询任务状态
  Timer.periodic(Duration(seconds: 2), (timer) async {
    final status = await apiService.getTryOnTaskStatus(task.taskId);
    if (status.success && status.data!.status == 'completed') {
      timer.cancel();
      print('试衣完成: ${status.data!.resultUrl}');
    }
  });
}
```

### 2. 错误处理

```dart
try {
  final result = await apiService.getClothingList();
  if (result.success) {
    // 处理成功响应
    handleClothingList(result.data!);
  } else {
    // 处理业务错误
    showError(result.error!);
  }
} catch (e) {
  // 处理网络异常
  showNetworkError(e.toString());
}
```

### 3. 最佳实践

1. **缓存策略**: 对服装列表等相对静态的数据进行缓存
2. **图片优化**: 使用缩略图提升加载速度
3. **错误重试**: 对网络错误实现自动重试机制
4. **用户体验**: 提供加载状态和进度反馈
5. **离线支持**: 缓存用户偏好和历史数据

---

## 📞 技术支持

如有接口使用问题，请联系：
- **技术文档**: https://docs.api.example.com
- **邮箱**: <EMAIL>
- **QQ群**: 123456789

---

*本文档最后更新时间: 2024-01-01* 