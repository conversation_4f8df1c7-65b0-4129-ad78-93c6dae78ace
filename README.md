# 智能试衣镜 (Smart Virtual Try-On Mirror)

一个基于Flutter的智能试衣镜应用，专为镜子设备设计，提供AI虚拟试衣、语音控制、手势交互等功能。

## 🌟 项目特色

- **镜子模式专用UI** - 专为智能镜子设备优化的用户界面
- **AI虚拟试衣** - 集成阿里云OutfitAnyone API，支持真实的AI试衣效果
- **智能语音控制** - 全程语音操作，支持20+语音命令
- **高级手势识别** - 支持滑动、缩放、多指触控等复杂手势
- **多种获取方式** - 二维码、邮箱、蓝牙等适合镜子的分享方式
- **本地数据管理** - 完整的数据库设计，支持用户偏好和历史记录

## 🎯 核心功能

### 📸 智能拍照
- 前置镜像相机预览
- 语音拍照命令
- 手势拍照控制
- 照片确认与重拍

### 👕 服装选择
- 多类别服装浏览（上衣、下装、裙装、外套）
- 颜色筛选（红、蓝、绿、黄、黑、白、粉、紫等）
- 风格分类（休闲、正式、运动、时尚等）
- 尺寸选择（XS-XXL）
- AI智能推荐

### 🤖 AI试衣处理
- 真实AI模型集成（OutfitAnyone）
- 高质量试衣效果生成
- 面部特征保持
- 服装自动调整适配
- 处理进度实时显示

### 📱 照片获取
- **二维码分享** - 生成二维码供手机扫描
- **邮箱发送** - 直接发送到用户邮箱
- **蓝牙传输** - 通过蓝牙发送到配对设备
- **本地存储** - 保存到镜子本地存储
- **相册保存** - 保存到系统相册

### 🎤 语音控制
支持以下语音命令：
- `"拍照"` - 开始拍摄
- `"选衣服"` - 进入服装选择
- `"红色上衣"` - 按颜色和类型筛选
- `"休闲风格"` - 按风格筛选
- `"确认"` - 确认当前操作
- `"保存"` - 保存照片到相册
- `"获取照片"` - 显示获取选项
- `"二维码"` - 生成分享二维码
- `"发邮箱"` - 发送到邮箱
- `"AI推荐"` - 获取搭配建议
- `"重新试衣"` - 重新开始流程

### 👋 手势控制
- **单击** - 确认选择
- **双击** - 快速操作
- **长按** - 显示更多选项
- **左右滑动** - 切换服装/选项
- **上下滑动** - 滚动浏览
- **双指缩放** - 放大/缩小预览
- **双指点击** - 特殊功能
- **三指点击** - 高级操作

## 🏗️ 技术架构

### 前端框架
- **Flutter 3.7+** - 跨平台UI框架
- **Provider** - 状态管理
- **Camera Plugin** - 相机功能
- **Permission Handler** - 权限管理

### 核心服务
- **AITryOnService** - AI试衣服务（集成阿里云API）
- **MirrorService** - 镜子专用功能服务
- **AIAssistantService** - 语音助手服务
- **GalleryService** - 相册保存服务

### 数据库设计
完整的MySQL数据库架构：
- **用户管理** - users, user_preferences
- **服装数据** - clothing_categories, brands, clothing_items
- **试衣历史** - tryon_history, ai_recommendations
- **系统配置** - system_configs, error_logs

### API架构
- **用户认证API** - 注册、登录、JWT验证
- **服装管理API** - 服装CRUD、搜索、推荐
- **AI试衣API** - 任务提交、状态查询、结果获取
- **文件上传API** - 图片上传、存储管理

## 📁 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   ├── clothing_item.dart    # 服装模型
│   └── user_model.dart       # 用户模型
├── screens/                  # 页面组件
│   ├── mirror_camera_screen.dart        # 镜子相机界面
│   ├── mirror_clothing_selection_screen.dart  # 服装选择界面
│   ├── mirror_processing_screen.dart    # AI处理界面
│   └── mirror_result_screen.dart        # 结果展示界面
├── services/                 # 服务层
│   ├── ai_tryon_service.dart # AI试衣服务
│   ├── mirror_service.dart   # 镜子专用服务
│   ├── ai_assistant_service.dart # 语音助手
│   ├── gallery_service.dart  # 相册服务
│   └── api_service.dart      # API服务
├── providers/                # 状态管理
│   └── app_state.dart        # 全局状态
├── widgets/                  # 公共组件
│   ├── gesture_detector_widget.dart # 手势检测组件
│   └── voice_control_widget.dart    # 语音控制组件
├── data/                     # 数据层
│   └── sample_clothing.dart  # 示例服装数据
└── utils/                    # 工具类
    └── constants.dart        # 常量定义

database/                     # 数据库相关
├── init.sql                  # 数据库初始化脚本
└── schemas/                  # 数据库模式文件

docs/                         # 文档
├── API_DOCUMENTATION.md      # API文档
├── API_OVERVIEW.md          # API概览
└── DATABASE_DESIGN.md       # 数据库设计文档
```

## 🚀 快速开始

### 环境要求
- Flutter 3.7.0 或更高版本
- Dart 3.0.0 或更高版本
- Android Studio / VS Code
- Android API 21+ / iOS 11.0+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd virtual_try_on
```

2. **安装依赖**
```bash
flutter pub get
```

3. **配置权限**

Android (`android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
```

iOS (`ios/Runner/Info.plist`):
```xml
<key>NSCameraUsageDescription</key>
<string>智能试衣镜需要访问相机进行拍照</string>
<key>NSPhotoLibraryAddUsageDescription</key>
<string>智能试衣镜需要保存试衣照片到相册</string>
```

4. **配置AI服务**

在 `lib/services/ai_tryon_service.dart` 中配置您的API密钥：
```dart
static const String _accessKey = 'your_access_key_here';
static const String _secretKey = 'your_secret_key_here';
```

5. **数据库配置**
```bash
# 导入数据库结构
mysql -u username -p database_name < database/init.sql
```

6. **运行应用**
```bash
# 开发模式
flutter run

# 发布模式
flutter run --release
```

## 📚 使用说明

### 镜子操作流程

1. **启动应用** - 应用自动打开相机预览
2. **站到镜子前** - 确保人像清晰可见
3. **拍摄照片** - 说"拍照"或点击拍照按钮
4. **确认照片** - 说"确认"或点击确认按钮
5. **选择服装** - 浏览服装，可使用语音筛选
6. **开始试衣** - 说"确认"开始AI处理
7. **查看结果** - 试衣完成后查看效果
8. **获取照片** - 选择获取方式（二维码、邮箱等）

### 语音交互示例

```
用户: "拍照"
系统: "好的，请准备拍照" [拍摄照片]

用户: "确认"
系统: "照片确认完成，请选择服装"

用户: "红色上衣"
系统: "为您筛选红色上衣" [显示相关服装]

用户: "确认试衣"
系统: "开始AI试衣处理，请稍等" [开始处理]

用户: "保存"
系统: "照片已保存到相册"

用户: "二维码"
系统: "二维码已生成，请用手机扫描"
```

## 🔧 配置说明

### AI服务配置

项目支持阿里云OutfitAnyone API集成：

1. 注册阿里云账号并开通OutfitAnyone服务
2. 获取AccessKey和SecretKey
3. 在`ai_tryon_service.dart`中配置密钥
4. 根据需要调整AI处理参数

### 镜子设备适配

针对不同尺寸的镜子设备，可以调整以下参数：

```dart
// 在 constants.dart 中配置
class MirrorConstants {
  static const double screenWidth = 1080.0;   // 镜子屏幕宽度
  static const double screenHeight = 1920.0;  // 镜子屏幕高度
  static const double cameraAspectRatio = 9/16; // 相机比例
}
```

## 🎨 UI定制

### 主题配置
```dart
ThemeData(
  primaryColor: Colors.black,
  backgroundColor: Colors.black,
  // 镜子专用深色主题
)
```

### 手势灵敏度调整
```dart
// 在 gesture_detector_widget.dart 中调整
static const double _swipeThreshold = 50.0;     // 滑动阈值
static const double _zoomThreshold = 0.1;       // 缩放阈值
static const int _doubleTapTimeLimit = 300;     // 双击时间限制
```

## 🛠️ 开发指南

### 添加新的语音命令

1. 在`VoiceCommandType`枚举中添加新类型
2. 在`processVoiceInput`方法中添加识别逻辑
3. 在相应的屏幕中处理该命令

### 扩展服装数据

1. 修改`ClothingItem`模型
2. 更新`sample_clothing.dart`中的数据
3. 调整数据库schema（如需要）

### 集成新的AI服务

1. 实现新的AI服务类
2. 在`ai_tryon_service.dart`中添加适配器
3. 更新配置参数

## 📊 性能优化

- **图片压缩** - 自动压缩用户照片以提高处理速度
- **缓存机制** - 服装图片本地缓存
- **异步处理** - AI处理使用异步调用
- **内存管理** - 及时释放大图片资源
- **网络优化** - 支持断网重试和进度显示

## 🔐 隐私安全

- **本地处理** - 优先使用本地处理，减少数据传输
- **数据加密** - 用户照片传输使用HTTPS加密
- **权限最小化** - 只申请必要的系统权限
- **自动清理** - 定期清理临时文件和过期数据

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持与反馈

- 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 功能建议: [GitHub Discussions](https://github.com/your-repo/discussions)
- 技术支持: <EMAIL>

## 🏆 致谢

- Flutter团队提供的优秀框架
- 阿里云提供的OutfitAnyone AI服务
- 开源社区的各种优秀插件

---

**智能试衣镜 - 让试衣变得更智能、更便捷！** 🎯✨
