const { chromium } = require('@playwright/test');

(async () => {
  // 启动浏览器
  const browser = await chromium.launch({
    headless: false // 设置为false以便你可以看到浏览器窗口
  });
  
  // 创建一个新的浏览器上下文
  const context = await browser.newContext();
  
  // 打开一个新页面
  const page = await context.newPage();
  
  // 访问你的Flutter Web应用
  await page.goto('http://localhost:56597/');
  
  console.log('成功访问虚拟试衣应用');
  
  // 等待页面加载完成
  await page.waitForLoadState('networkidle');
  
  // 截图
  await page.screenshot({ path: 'screenshot.png' });
  
  console.log('已保存截图');
  
  // 等待10秒，以便你可以看到页面
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // 关闭浏览器
  await browser.close();
})();
