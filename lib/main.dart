import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/app_state.dart';

// 只导入需要的镜子模式界面
import 'screens/mirror_camera_screen.dart';
import 'screens/photo_confirm_screen.dart';
import 'screens/mirror_clothing_selection_screen.dart';
import 'screens/mirror_processing_screen.dart';
import 'screens/mirror_result_screen.dart';
import 'screens/error_screen.dart';

void main() {
  runApp(const VirtualTryOnApp());
}

class VirtualTryOnApp extends StatelessWidget {
  const VirtualTryOnApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        final appState = AppState();
        // 初始化相机
        appState.initializeCameras();
        return appState;
      },
      child: MaterialApp(
        title: '智能试衣镜',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          // 删除指定字体，让Flutter使用系统默认字体
          useMaterial3: true,
          // 针对镜子显示优化的主题
          brightness: Brightness.dark,
          scaffoldBackgroundColor: const Color(0xFF000000),
          visualDensity: VisualDensity.comfortable,
          // 为Web优化的字体渲染
          typography: Typography.material2021(),
        ),
        home: const MainScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        // 总是使用新设计的镜子界面
        
        switch (appState.currentScreen) {
          case AppScreen.camera:
            return const MirrorCameraScreen(); // 总是使用镜子界面
          case AppScreen.photoConfirm:
            return const PhotoConfirmScreen();
          case AppScreen.clothingSelection:
            return const MirrorClothingSelectionScreen(); // 总是使用镜子界面
          case AppScreen.processing:
            return const MirrorProcessingScreen(); // 总是使用镜子界面
          case AppScreen.result:
            return const MirrorResultScreen(); // 总是使用镜子界面
          case AppScreen.error:
            return const ErrorScreen();
        }
      },
    );
  }
}
