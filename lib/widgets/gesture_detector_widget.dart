import 'package:flutter/material.dart';

class GestureDetectorWidget extends StatefulWidget {
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final VoidCallback? onSwipeUp;
  final VoidCallback? onSwipeDown;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onTap;
  final Function(double)? onPinchZoom;
  final VoidCallback? onTwoFingerTap;
  final VoidCallback? onThreeFingerTap;
  final Function(Offset)? onDragMove;
  final Widget child;

  const GestureDetectorWidget({
    super.key,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.onSwipeUp,
    this.onSwipeDown,
    this.onDoubleTap,
    this.onLongPress,
    this.onTap,
    this.onPinchZoom,
    this.onTwoFingerTap,
    this.onThreeFingerTap,
    this.onDragMove,
    required this.child,
  });

  @override
  State<GestureDetectorWidget> createState() => _GestureDetectorWidgetState();
}

class _GestureDetectorWidgetState extends State<GestureDetectorWidget>
    with TickerProviderStateMixin {
  late AnimationController _tapAnimationController;
  late Animation<double> _tapAnimation;
  
  // 手势识别相关变量
  Offset? _startPanPosition;
  Offset? _lastFocalPoint;
  DateTime? _lastTapTime;
  int _tapCount = 0;
  double _initialScale = 1.0;
  double _currentScale = 1.0;
  int _pointerCount = 0;
  final Map<int, Offset> _pointerPositions = {};
  
  // 手势阈值
  static const double _swipeThreshold = 50.0;
  static const int _doubleTapTimeLimit = 300; // 毫秒
  static const double _zoomThreshold = 0.1;
  // static const double _multiTouchThreshold = 20.0; // 暂时未使用

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _tapAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _tapAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _tapAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _tapAnimationController.dispose();
    super.dispose();
  }



  void _handleTap() {
    final now = DateTime.now();
    
    if (_lastTapTime != null && 
        now.difference(_lastTapTime!).inMilliseconds < _doubleTapTimeLimit) {
      _tapCount++;
    } else {
      _tapCount = 1;
    }
    
    _lastTapTime = now;
    
    // 播放点击动画
    _tapAnimationController.forward().then((_) {
      _tapAnimationController.reverse();
    });
    
    // 延迟判断是单击还是双击
    Future.delayed(Duration(milliseconds: _doubleTapTimeLimit), () {
      if (_tapCount == 1) {
        _triggerTap();
      } else if (_tapCount >= 2) {
        _triggerDoubleTap();
      }
      _tapCount = 0;
    });
  }

  void _handleLongPress() {
    // 长按震动反馈（如果设备支持）
    _triggerLongPress();
  }

  // 手势触发方法
  void _triggerSwipeLeft() {
    widget.onSwipeLeft?.call();
    _showGestureHint('向左滑动', Icons.swipe_left);
  }

  void _triggerSwipeRight() {
    widget.onSwipeRight?.call();
    _showGestureHint('向右滑动', Icons.swipe_right);
  }

  void _triggerSwipeUp() {
    widget.onSwipeUp?.call();
    _showGestureHint('向上滑动', Icons.swipe_up);
  }

  void _triggerSwipeDown() {
    widget.onSwipeDown?.call();
    _showGestureHint('向下滑动', Icons.swipe_down);
  }

  void _triggerTap() {
    widget.onTap?.call();
  }

  void _triggerDoubleTap() {
    widget.onDoubleTap?.call();
    _showGestureHint('双击', Icons.touch_app);
  }

  void _triggerLongPress() {
    widget.onLongPress?.call();
    _showGestureHint('长按', Icons.touch_app);
  }

  // 新增手势处理方法
  void _handleScaleStart(ScaleStartDetails details) {
    _initialScale = _currentScale;
    _pointerCount = details.pointerCount;
    _startPanPosition = details.localFocalPoint;
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    _lastFocalPoint = details.localFocalPoint;
    
    if (details.pointerCount == 2 && widget.onPinchZoom != null) {
      // 双指缩放
      final newScale = _initialScale * details.scale;
      final scaleDelta = newScale - _currentScale;
      
      if (scaleDelta.abs() > _zoomThreshold) {
        _currentScale = newScale;
        widget.onPinchZoom!(scaleDelta);
      }
    } else if (details.pointerCount == 1) {
      // 单指拖拽
      if (widget.onDragMove != null) {
        widget.onDragMove!(details.localFocalPoint);
      }
    }
  }

  void _handleScaleEnd(ScaleEndDetails details) {
    // 检测滑动手势（单指）
    if (_pointerCount == 1 && _startPanPosition != null && _lastFocalPoint != null) {
      final deltaX = _lastFocalPoint!.dx - _startPanPosition!.dx;
      final deltaY = _lastFocalPoint!.dy - _startPanPosition!.dy;
      
      // 判断是否超过滑动阈值
      if (deltaX.abs() > _swipeThreshold || deltaY.abs() > _swipeThreshold) {
        // 确定主要方向
        if (deltaX.abs() > deltaY.abs()) {
          // 水平滑动
          if (deltaX > 0) {
            _triggerSwipeRight();
          } else {
            _triggerSwipeLeft();
          }
        } else {
          // 垂直滑动
          if (deltaY > 0) {
            _triggerSwipeDown();
          } else {
            _triggerSwipeUp();
          }
        }
      }
    }
    
    // 检测多指点击
    if (_pointerCount == 2) {
      widget.onTwoFingerTap?.call();
      _showGestureHint('双指点击', Icons.touch_app);
    } else if (_pointerCount == 3) {
      widget.onThreeFingerTap?.call();
      _showGestureHint('三指点击', Icons.touch_app);
    }
    
    _startPanPosition = null;
    _lastFocalPoint = null;
  }

  void _handlePointerDown(PointerDownEvent event) {
    _pointerPositions[event.pointer] = event.localPosition;
  }

  void _handlePointerUp(PointerUpEvent event) {
    _pointerPositions.remove(event.pointer);
  }

  void _handlePointerMove(PointerMoveEvent event) {
    _pointerPositions[event.pointer] = event.localPosition;
    
    // 检测复杂手势模式
    if (_pointerPositions.length >= 2) {
      _detectAdvancedGestures();
    }
  }

  void _detectAdvancedGestures() {
    if (_pointerPositions.length == 2) {
      // final positions = _pointerPositions.values.toList();
      // final distance = (positions[0] - positions[1]).distance;
      
      // 可以在这里添加更复杂的手势识别逻辑
      // 比如旋转手势、特定形状手势等
      // 目前暂时注释相关变量以避免未使用警告
    }
  }

  void _showGestureHint(String gesture, IconData icon) {
    if (!mounted) return;
    
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    
    overlayEntry = OverlayEntry(
      builder: (context) => _buildGestureHintOverlay(gesture, icon, overlayEntry),
    );
    
    overlay.insert(overlayEntry);
    
    // 2秒后自动移除提示
    Future.delayed(const Duration(seconds: 2), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  Widget _buildGestureHintOverlay(String gesture, IconData icon, OverlayEntry entry) {
    return Positioned(
      top: 150,
      left: 0,
      right: 0,
      child: Material(
        color: Colors.transparent,
        child: Center(
          child: TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 300),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Opacity(
                  opacity: value,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.5),
                          blurRadius: 15,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          icon,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          gesture,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
            onEnd: () {
              // 动画结束后延迟移除
              Future.delayed(const Duration(milliseconds: 1500), () {
                if (entry.mounted) {
                  entry.remove();
                }
              });
            },
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _tapAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _tapAnimation.value,
          child: Listener(
            // 指针事件监听
            onPointerDown: _handlePointerDown,
            onPointerUp: _handlePointerUp,
            onPointerMove: _handlePointerMove,
            child: GestureDetector(
              // 基础手势
              onTap: _handleTap,
              onLongPress: _handleLongPress,
              
              // 使用scale手势替代pan手势（scale是pan的超集）
              onScaleStart: _handleScaleStart,
              onScaleUpdate: _handleScaleUpdate,
              onScaleEnd: _handleScaleEnd,
              
              // 确保手势检测区域覆盖整个屏幕
              behavior: HitTestBehavior.translucent,
              
              child: SizedBox.expand(
                child: widget.child,
              ),
            ),
          ),
        );
      },
    );
  }
}

// 手势识别增强器 - 提供更精确的手势识别
class EnhancedGestureRecognizer {
  static const double _velocityThreshold = 300.0;
  static const double _distanceThreshold = 80.0;
  
  static bool isValidSwipe(Offset delta, Velocity velocity) {
    final distance = delta.distance;
    final speed = velocity.pixelsPerSecond.distance;
    
    return distance > _distanceThreshold || speed > _velocityThreshold;
  }
  
  static String getSwipeDirection(Offset delta) {
    final dx = delta.dx;
    final dy = delta.dy;
    
    if (dx.abs() > dy.abs()) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'down' : 'up';
    }
  }
} 