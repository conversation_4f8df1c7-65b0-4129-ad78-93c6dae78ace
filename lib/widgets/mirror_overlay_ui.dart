import 'package:flutter/material.dart';

class MirrorOverlayUI extends StatefulWidget {
  final VoidCallback onClothingTap;
  final VoidCallback onCaptureTap;
  final VoidCallback onSettingsTap;

  const MirrorOverlayUI({
    super.key,
    required this.onClothingTap,
    required this.onCaptureTap,
    required this.onSettingsTap,
  });

  @override
  State<MirrorOverlayUI> createState() => _MirrorOverlayUIState();
}

class _MirrorOverlayUIState extends State<MirrorOverlayUI>
    with TickerProviderStateMixin {
  late AnimationController _breathingController;
  late Animation<double> _breathingAnimation;
  bool _showHelpText = true;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _hideHelpTextAfterDelay();
  }

  void _initAnimations() {
    _breathingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _breathingAnimation = Tween<double>(
      begin: 0.7,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _breathingController,
      curve: Curves.easeInOut,
    ));

    _breathingController.repeat(reverse: true);
  }

  void _hideHelpTextAfterDelay() {
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _showHelpText = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _breathingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Stack(
      children: [
        // 左侧服装选择按钮
        _buildLeftSideButton(screenHeight),
        
        // 右侧功能按钮组
        _buildRightSideButtons(screenHeight),
        
        // 底部拍照按钮
        _buildBottomCaptureButton(screenWidth),
        
        // 顶部状态指示器
        _buildTopStatusBar(),
        
        // 帮助文本（可隐藏）
        if (_showHelpText) _buildHelpText(),
        
        // 新功能提示
        _buildNewFeatureBanner(),
        
        // 镜子边框装饰
        _buildMirrorDecoration(),
      ],
    );
  }

  Widget _buildLeftSideButton(double screenHeight) {
    return Positioned(
      left: 20,
      top: screenHeight * 0.4,
      child: GestureDetector(
        onTap: widget.onClothingTap,
        child: AnimatedBuilder(
          animation: _breathingAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _breathingAnimation.value,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(40),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.4),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.checkroom,
                  color: Colors.white,
                  size: 35,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildRightSideButtons(double screenHeight) {
    return Positioned(
      right: 20,
      top: screenHeight * 0.25,
      child: Column(
        children: [
          // AI助手按钮
          _buildFloatingButton(
            icon: Icons.smart_toy,
            onTap: () {
              // AI助手功能
            },
          ),
          const SizedBox(height: 20),
          
          // 风格推荐按钮
          _buildFloatingButton(
            icon: Icons.auto_awesome,
            onTap: () {
              // 风格推荐功能
            },
          ),
          const SizedBox(height: 20),
          
          // 设置按钮
          _buildFloatingButton(
            icon: Icons.settings,
            onTap: widget.onSettingsTap,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.12),
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white.withValues(alpha: 0.9),
          size: 28,
        ),
      ),
    );
  }

  Widget _buildBottomCaptureButton(double screenWidth) {
    return Positioned(
      bottom: 50,
      left: screenWidth * 0.5 - 40,
      child: GestureDetector(
        onTap: widget.onCaptureTap,
        child: AnimatedBuilder(
          animation: _breathingAnimation,
          builder: (context, child) {
            return Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(40),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 内圆
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  // 拍照图标
                  const Icon(
                    Icons.camera_alt,
                    color: Color(0xFF1A1A1A),
                    size: 30,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTopStatusBar() {
    return Positioned(
      top: 50,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 时间显示
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                TimeOfDay.now().format(context),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            
            // 连接状态指示
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.4),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'AI已连接',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpText() {
    return Positioned(
      bottom: 150,
      left: 0,
      right: 0,
      child: AnimatedOpacity(
        opacity: _showHelpText ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 500),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '手势操作指南',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '← 滑动选择服装 | 双击拍照 | 长按设置 →',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMirrorDecoration() {
    return Positioned.fill(
      child: IgnorePointer(
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 2,
            ),
            borderRadius: BorderRadius.circular(0),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.05),
                blurRadius: 30,
                spreadRadius: -10,
                offset: const Offset(0, 0),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewFeatureBanner() {
    return Positioned(
      bottom: 180, // 提高位置，避免与拍摄按钮冲突
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          margin: const EdgeInsets.symmetric(horizontal: 40),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.blue.withValues(alpha: 0.7),
                Colors.cyan.withValues(alpha: 0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.cyan.withValues(alpha: 0.2),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.auto_awesome,
                color: Colors.white.withValues(alpha: 0.9),
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                '镜子模式 - 支持手势操作',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.95),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 