import '../models/clothing_item.dart';

class SampleClothing {
  static List<ClothingItem> getAllClothing() {
    return [
      // 上衣
      ClothingItem(
        id: '1',
        name: '白色基础T恤',
        description: '经典百搭白色圆领T恤，舒适透气',
        category: ClothingCategory.tops,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '2',
        name: '蓝色牛仔衬衫',
        description: '休闲牛仔衬衫，时尚百搭',
        category: ClothingCategory.tops,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '3',
        name: '黑色polo衫',
        description: '商务休闲polo衫，简约大方',
        category: ClothingCategory.tops,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '4',
        name: '条纹长袖衫',
        description: '经典条纹设计，法式优雅',
        category: ClothingCategory.tops,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '5',
        name: '粉色卫衣',
        description: '舒适连帽卫衣，青春活力',
        category: ClothingCategory.tops,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '6',
        name: '格子衬衫',
        description: '复古格子图案，文艺范十足',
        category: ClothingCategory.tops,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      
      // 连衣裙
      ClothingItem(
        id: '7',
        name: '小黑裙',
        description: '经典小黑裙，优雅知性',
        category: ClothingCategory.dresses,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '8',
        name: '碎花连衣裙',
        description: '浪漫碎花图案，甜美可爱',
        category: ClothingCategory.dresses,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '9',
        name: '白色雪纺裙',
        description: '轻盈雪纺材质，仙气飘飘',
        category: ClothingCategory.dresses,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '10',
        name: '蓝色牛仔裙',
        description: '休闲牛仔连衣裙，青春活力',
        category: ClothingCategory.dresses,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '11',
        name: '红色晚礼服',
        description: '优雅晚礼服，正式场合首选',
        category: ClothingCategory.dresses,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '12',
        name: '条纹连衣裙',
        description: '经典条纹设计，简约时尚',
        category: ClothingCategory.dresses,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      
      // 外套
      ClothingItem(
        id: '13',
        name: '黑色西装外套',
        description: '商务正装外套，专业干练',
        category: ClothingCategory.outerwear,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '14',
        name: '牛仔外套',
        description: '经典牛仔外套，百搭单品',
        category: ClothingCategory.outerwear,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '15',
        name: '风衣',
        description: '经典风衣，优雅大方',
        category: ClothingCategory.outerwear,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '16',
        name: '羽绒服',
        description: '保暖羽绒服，冬季必备',
        category: ClothingCategory.outerwear,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '17',
        name: '皮夹克',
        description: '酷炫皮夹克，个性十足',
        category: ClothingCategory.outerwear,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
      ClothingItem(
        id: '18',
        name: '针织开衫',
        description: '温柔针织开衫，舒适保暖',
        category: ClothingCategory.outerwear,
        imagePath: 'assets/images/clothing/placeholder.svg',
      ),
    ];
  }
  
  static List<ClothingItem> getClothingByCategory(String category) {
    return getAllClothing().where((item) => item.category == category).toList();
  }
}