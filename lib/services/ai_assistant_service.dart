import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/clothing_item.dart';

class AIAssistantService {
  static final AIAssistantService _instance = AIAssistantService._internal();
  factory AIAssistantService() => _instance;
  AIAssistantService._internal();

  bool _isListening = false;
  bool _isInitialized = false;

  bool get isListening => _isListening;
  bool get isInitialized => _isInitialized;

  // 初始化AI助手
  Future<bool> initialize() async {
    try {
      // 这里可以初始化语音识别引擎
      // 目前先模拟初始化成功
      await Future.delayed(const Duration(seconds: 1));
      _isInitialized = true;
      return true;
    } catch (e) {
      // Debug: AI助手初始化失败: $e (remove in production)
      return false;
    }
  }

  // 开始语音识别
  Future<void> startListening() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    _isListening = true;
    // 提供语音反馈
    await provideVoiceFeedback('我在听，请说话');
    
    // 这里实现语音识别逻辑
    // 目前先模拟
    await Future.delayed(const Duration(seconds: 3));
    _isListening = false;
  }

  // 停止语音识别
  void stopListening() {
    _isListening = false;
  }

  // 提供语音反馈
  Future<void> provideVoiceFeedback(String message) async {
    try {
      // 实际项目中可以集成TTS（文字转语音）引擎
      // 这里模拟语音输出
      if (kDebugMode) {
        debugPrint('语音输出: $message');
      }
      
      // 模拟语音播放时间
      await Future.delayed(Duration(milliseconds: message.length * 100));
    } catch (e) {
      if (kDebugMode) {
        debugPrint('语音反馈失败: $e');
      }
    }
  }

  // 处理语音命令
  VoiceCommand? processVoiceInput(String voiceText) {
    final text = voiceText.toLowerCase().trim();
    
    // 服装相关命令
    if (text.contains('换衣服') || text.contains('试衣') || text.contains('选择服装') || text.contains('选衣服')) {
      return VoiceCommand(
        type: VoiceCommandType.selectClothing,
        action: 'show_clothing_selection',
        parameters: {},
      );
    }
    
    // 拍照命令
    if (text.contains('拍照') || text.contains('照相') || text.contains('拍一张') || text.contains('开始拍照')) {
      return VoiceCommand(
        type: VoiceCommandType.takePhoto,
        action: 'capture_photo',
        parameters: {},
      );
    }
    
    // 确认命令
    if (text.contains('确认') || text.contains('确定') || text.contains('好的') || text.contains('是的') || text.contains('开始试衣')) {
      return VoiceCommand(
        type: VoiceCommandType.confirm,
        action: 'confirm_action',
        parameters: {},
      );
    }
    
    // 取消命令
    if (text.contains('取消') || text.contains('不要') || text.contains('重新来') || text.contains('不确认')) {
      return VoiceCommand(
        type: VoiceCommandType.cancel,
        action: 'cancel_action',
        parameters: {},
      );
    }
    
    // 保存命令
    if (text.contains('保存') || text.contains('存到相册') || text.contains('保存照片')) {
      return VoiceCommand(
        type: VoiceCommandType.save,
        action: 'save_photo',
        parameters: {},
      );
    }
    
    // 分享命令（适合镜子的获取照片功能）
    if (text.contains('获取照片') || text.contains('分享') || text.contains('发送给我') || text.contains('传给我')) {
      return VoiceCommand(
        type: VoiceCommandType.share,
        action: 'get_photo_options',
        parameters: {},
      );
    }
    
    // 邮箱发送命令
    if (text.contains('发邮箱') || text.contains('邮件发送') || text.contains('发到邮箱')) {
      return VoiceCommand(
        type: VoiceCommandType.sendEmail,
        action: 'send_email',
        parameters: {},
      );
    }
    
    // 二维码命令
    if (text.contains('二维码') || text.contains('扫码') || text.contains('生成二维码')) {
      return VoiceCommand(
        type: VoiceCommandType.generateQR,
        action: 'generate_qr_code',
        parameters: {},
      );
    }
    
    // 颜色选择
    if (text.contains('红色') || text.contains('蓝色') || text.contains('绿色') || 
        text.contains('黄色') || text.contains('黑色') || text.contains('白色') ||
        text.contains('粉色') || text.contains('紫色') || text.contains('橙色') ||
        text.contains('灰色') || text.contains('棕色')) {
      final color = _extractColor(text);
      return VoiceCommand(
        type: VoiceCommandType.selectColor,
        action: 'filter_by_color',
        parameters: {'color': color},
      );
    }
    
    // 服装类型选择
    if (text.contains('上衣') || text.contains('衬衫') || text.contains('T恤') ||
        text.contains('下装') || text.contains('裤子') || text.contains('裙子') ||
        text.contains('外套') || text.contains('连衣裙') || text.contains('牛仔') ||
        text.contains('短袖') || text.contains('长袖')) {
      final category = _extractClothingCategory(text);
      return VoiceCommand(
        type: VoiceCommandType.selectCategory,
        action: 'filter_by_category',
        parameters: {'category': category},
      );
    }
    
    // 风格选择
    if (text.contains('休闲') || text.contains('正式') || text.contains('运动') || 
        text.contains('时尚') || text.contains('商务') || text.contains('可爱') ||
        text.contains('优雅') || text.contains('帅气') || text.contains('甜美')) {
      final style = _extractStyle(text);
      return VoiceCommand(
        type: VoiceCommandType.selectStyle,
        action: 'filter_by_style',
        parameters: {'style': style},
      );
    }
    
    // 尺寸选择
    if (text.contains('小码') || text.contains('中码') || text.contains('大码') ||
        text.contains('XS') || text.contains('S码') || text.contains('M码') ||
        text.contains('L码') || text.contains('XL') || text.contains('XXL')) {
      final size = _extractSize(text);
      return VoiceCommand(
        type: VoiceCommandType.selectSize,
        action: 'filter_by_size',
        parameters: {'size': size},
      );
    }
    
    // 相机控制
    if (text.contains('切换相机') || text.contains('前置摄像头') || text.contains('后置摄像头')) {
      return VoiceCommand(
        type: VoiceCommandType.switchCamera,
        action: 'switch_camera',
        parameters: {},
      );
    }
    
    // 导航命令
    if (text.contains('返回') || text.contains('回去') || text.contains('上一步') || text.contains('重新选择')) {
      return VoiceCommand(
        type: VoiceCommandType.navigation,
        action: 'go_back',
        parameters: {},
      );
    }
    
    // 重新开始
    if (text.contains('重新开始') || text.contains('从头开始') || text.contains('重新试衣')) {
      return VoiceCommand(
        type: VoiceCommandType.restart,
        action: 'restart_process',
        parameters: {},
      );
    }
    
    // 帮助命令
    if (text.contains('帮助') || text.contains('怎么用') || text.contains('教程') || text.contains('说明')) {
      return VoiceCommand(
        type: VoiceCommandType.help,
        action: 'show_help',
        parameters: {},
      );
    }
    
    // AI建议
    if (text.contains('推荐') || text.contains('建议') || text.contains('搭配') || text.contains('AI推荐')) {
      return VoiceCommand(
        type: VoiceCommandType.aiSuggestion,
        action: 'show_ai_suggestions',
        parameters: {},
      );
    }
    
    return null; // 无法识别的命令
  }

  // 根据当前屏幕提供智能语音提示
  Future<String> getSmartVoicePrompt(String currentScreen) async {
    switch (currentScreen) {
      case 'camera':
        return '请站在镜子前，说"拍照"开始试衣，或说"选衣服"浏览服装';
      case 'photoConfirm':
        return '照片已拍摄完成，说"确认"继续，或说"重新拍照"重拍';
      case 'clothingSelection':
        return '请选择您喜欢的服装，可以说出颜色、风格或类型，比如"红色上衣"或"休闲风格"';
      case 'processing':
        return 'AI正在为您生成试衣效果，请稍等片刻';
      case 'result':
        return '试衣完成！说"保存"保存照片，"获取照片"获取到手机，或"重新试衣"换其他服装';
      case 'error':
        return '出现了错误，说"重新开始"重新试衣，或说"帮助"获取使用说明';
      default:
        return '您好，我是智能试衣助手，说"帮助"了解如何使用';
    }
  }

  // 获取语音操作建议
  List<String> getVoiceOperationTips(String currentScreen) {
    switch (currentScreen) {
      case 'camera':
        return [
          '说"拍照"开始拍摄',
          '说"切换相机"切换摄像头',
          '说"帮助"获取使用说明'
        ];
      case 'photoConfirm':
        return [
          '说"确认"使用这张照片',
          '说"重新拍照"重新拍摄',
          '说"返回"回到相机界面'
        ];
      case 'clothingSelection':
        return [
          '说"红色上衣"选择颜色和类型',
          '说"休闲风格"选择风格',
          '说"推荐"获取AI搭配建议',
          '说"确认"开始试衣'
        ];
      case 'result':
        return [
          '说"保存"保存到相册',
          '说"获取照片"获取到手机',
          '说"二维码"生成分享码',
          '说"重新试衣"尝试其他服装'
        ];
      default:
        return [
          '说"帮助"获取使用说明',
          '说"开始试衣"开始使用'
        ];
    }
  }

  // 生成AI风格建议
  Future<List<StyleSuggestion>> generateStyleSuggestions({
    required String userAge,
    required String userGender,
    required String occasion,
    required String season,
  }) async {
    // 模拟AI分析延迟
    await Future.delayed(const Duration(seconds: 2));
    
    final suggestions = <StyleSuggestion>[
      StyleSuggestion(
        title: '商务休闲风',
        description: '适合日常工作和社交场合，既专业又不失亲和力',
        confidence: 0.95,
        clothingItems: ['衬衫', '休闲西装', '九分裤'],
        colors: ['深蓝', '白色', '米色'],
        accessories: ['简约手表', '皮质公文包'],
      ),
      StyleSuggestion(
        title: '时尚街头风',
        description: '年轻活力，展现个性与创意的穿搭风格',
        confidence: 0.88,
        clothingItems: ['印花T恤', '牛仔外套', '破洞牛仔裤'],
        colors: ['黑色', '白色', '亮色点缀'],
        accessories: ['棒球帽', '运动鞋', '单肩包'],
      ),
      StyleSuggestion(
        title: '优雅知性风',
        description: '展现女性的优雅气质和知性美感',
        confidence: 0.82,
        clothingItems: ['丝质衬衫', '铅笔裙', '小香风外套'],
        colors: ['经典黑', '珍珠白', '温柔粉'],
        accessories: ['珍珠项链', '高跟鞋', '小手袋'],
      ),
    ];
    
    return suggestions;
  }

  // 分析服装搭配
  Future<MatchingAnalysis> analyzeClothingMatch(
    ClothingItem selectedItem,
    List<ClothingItem> wardrobe,
  ) async {
    await Future.delayed(const Duration(seconds: 1));
    
    // 模拟AI分析
    final matchingItems = wardrobe.where((item) => 
      item.category != selectedItem.category).take(3).toList();
    
    return MatchingAnalysis(
      matchScore: 0.87,
      matchingItems: matchingItems,
      recommendations: [
        '这件${selectedItem.name}很适合您的肤色',
        '建议搭配深色下装以形成对比',
        '可以加入一些配饰来提升整体层次感',
      ],
      colorAnalysis: ColorAnalysis(
        dominantColor: '蓝色',
        complementaryColors: ['白色', '米色', '深灰'],
        seasonType: '春季型',
      ),
    );
  }

  // 提供穿搭建议
  List<String> getWearingTips(String occasion, String weather) {
    final tips = <String>[];
    
    switch (occasion.toLowerCase()) {
      case '工作':
      case '商务':
        tips.addAll([
          '选择经典色彩，如黑、白、灰、蓝',
          '注意服装的合身度和整洁度',
          '配饰选择要简约而精致',
        ]);
        break;
      case '约会':
      case '社交':
        tips.addAll([
          '可以选择一些亮色来增加亲和力',
          '注意穿搭的层次感和细节',
          '选择能展现个人特色的单品',
        ]);
        break;
      case '休闲':
      case '日常':
        tips.addAll([
          '舒适度是第一要素',
          '可以尝试混搭不同风格',
          '根据活动选择合适的鞋子',
        ]);
        break;
    }
    
    // 根据天气添加建议
    switch (weather.toLowerCase()) {
      case '炎热':
      case '夏天':
        tips.add('选择透气性好的面料');
        tips.add('浅色服装有助于反射阳光');
        break;
      case '寒冷':
      case '冬天':
        tips.add('注意保暖，可以选择分层穿搭');
        tips.add('深色服装有助于吸收热量');
        break;
      case '下雨':
        tips.add('选择防水材质或携带雨具');
        tips.add('避免容易湿透的浅色服装');
        break;
    }
    
    return tips;
  }

  // 提取颜色信息
  String _extractColor(String text) {
    if (text.contains('红')) return '红色';
    if (text.contains('蓝')) return '蓝色';
    if (text.contains('绿')) return '绿色';
    if (text.contains('黄')) return '黄色';
    if (text.contains('黑')) return '黑色';
    if (text.contains('白')) return '白色';
    if (text.contains('粉')) return '粉色';
    if (text.contains('紫')) return '紫色';
    if (text.contains('橙')) return '橙色';
    if (text.contains('灰')) return '灰色';
    if (text.contains('棕')) return '棕色';
    return '其他';
  }

  // 提取风格信息
  String _extractStyle(String text) {
    if (text.contains('休闲')) return '休闲';
    if (text.contains('正式') || text.contains('商务')) return '正式';
    if (text.contains('运动')) return '运动';
    if (text.contains('时尚') || text.contains('潮流')) return '时尚';
    if (text.contains('甜美')) return '甜美';
    if (text.contains('酷帅') || text.contains('帅气')) return '酷帅';
    if (text.contains('可爱')) return '可爱';
    if (text.contains('优雅')) return '优雅';
    return '其他';
  }

  // 提取服装类型信息
  String _extractClothingCategory(String text) {
    if (text.contains('上衣') || text.contains('衬衫') || text.contains('T恤') || 
        text.contains('短袖') || text.contains('长袖')) {
      return 'tops';
    }
    if (text.contains('下装') || text.contains('裤子') || text.contains('牛仔')) {
      return 'bottoms';
    }
    if (text.contains('裙子') || text.contains('连衣裙')) {
      return 'dresses';
    }
    if (text.contains('外套')) {
      return 'outerwear';
    }
    return 'tops'; // 默认返回上衣
  }

  // 提取尺寸信息
  String _extractSize(String text) {
    if (text.contains('XS') || text.contains('小码')) return 'XS';
    if (text.contains('S码') || text.contains('S')) return 'S';
    if (text.contains('M码') || text.contains('M') || text.contains('中码')) return 'M';
    if (text.contains('L码') || text.contains('L') || text.contains('大码')) return 'L';
    if (text.contains('XL') || text.contains('超大')) return 'XL';
    if (text.contains('XXL')) return 'XXL';
    return 'M'; // 默认返回中码
  }

  // 触觉反馈
  void provideFeedback(FeedbackType type) {
    switch (type) {
      case FeedbackType.success:
        HapticFeedback.lightImpact();
        break;
      case FeedbackType.warning:
        HapticFeedback.mediumImpact();
        break;
      case FeedbackType.error:
        HapticFeedback.heavyImpact();
        break;
      case FeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }
}

// 语音命令数据类
class VoiceCommand {
  final VoiceCommandType type;
  final String action;
  final Map<String, dynamic> parameters;

  VoiceCommand({
    required this.type,
    required this.action,
    required this.parameters,
  });
}

enum VoiceCommandType {
  selectClothing,
  takePhoto,
  selectColor,
  selectStyle,
  selectCategory,
  selectSize,
  navigation,
  help,
  confirm,
  cancel,
  save,
  share,
  sendEmail,
  generateQR,
  switchCamera,
  restart,
  aiSuggestion,
}

// 风格建议数据类
class StyleSuggestion {
  final String title;
  final String description;
  final double confidence;
  final List<String> clothingItems;
  final List<String> colors;
  final List<String> accessories;

  StyleSuggestion({
    required this.title,
    required this.description,
    required this.confidence,
    required this.clothingItems,
    required this.colors,
    required this.accessories,
  });
}

// 搭配分析数据类
class MatchingAnalysis {
  final double matchScore;
  final List<ClothingItem> matchingItems;
  final List<String> recommendations;
  final ColorAnalysis colorAnalysis;

  MatchingAnalysis({
    required this.matchScore,
    required this.matchingItems,
    required this.recommendations,
    required this.colorAnalysis,
  });
}

// 颜色分析数据类
class ColorAnalysis {
  final String dominantColor;
  final List<String> complementaryColors;
  final String seasonType;

  ColorAnalysis({
    required this.dominantColor,
    required this.complementaryColors,
    required this.seasonType,
  });
}

enum FeedbackType {
  success,
  warning,
  error,
  selection,
} 