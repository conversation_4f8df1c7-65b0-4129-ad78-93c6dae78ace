import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';

class GalleryService {
  static final GalleryService _instance = GalleryService._internal();
  factory GalleryService() => _instance;
  GalleryService._internal();

  /// 保存图片到相册
  /// [imageBytes] - 图片字节数据
  /// [imagePath] - 图片文件路径 (移动端)
  /// [fileName] - 保存的文件名，默认为当前时间戳
  Future<SaveResult> saveImageToGallery({
    Uint8List? imageBytes,
    String? imagePath,
    String? fileName,
  }) async {
    try {
      // 检查参数
      if (imageBytes == null && imagePath == null) {
        return SaveResult.error('没有提供图片数据');
      }

      // 请求权限
      if (!await _requestPermissions()) {
        return SaveResult.error('未获得存储权限');
      }

      // 生成文件名
      final name = fileName ?? 'virtual_tryon_${DateTime.now().millisecondsSinceEpoch}';

      dynamic result;

      if (kIsWeb) {
        // Web端暂不支持保存到相册
        return SaveResult.error('Web端暂不支持保存到相册');
      } else if (imageBytes != null) {
        // 使用字节数据保存
        result = await ImageGallerySaver.saveImage(
          imageBytes,
          name: name,
          quality: 80,
        );
      } else if (imagePath != null) {
        // 使用文件路径保存
        final file = File(imagePath);
        if (!await file.exists()) {
          return SaveResult.error('图片文件不存在');
        }
        
        result = await ImageGallerySaver.saveFile(
          imagePath,
          name: name,
        );
      }

      // 检查保存结果
      if (result != null && result['isSuccess'] == true) {
        return SaveResult.success(
          message: '图片已保存到相册',
          filePath: result['filePath'],
        );
      } else {
        return SaveResult.error('保存失败: ${result?['errorMessage'] ?? '未知错误'}');
      }
    } catch (e) {
      return SaveResult.error('保存过程中发生错误: $e');
    }
  }

  /// 请求存储权限
  Future<bool> _requestPermissions() async {
    if (kIsWeb) {
      return false; // Web端不需要权限
    }

    // Android 权限请求
    if (Platform.isAndroid) {
      // Android 13+ 需要不同的权限
      if (await _isAndroid13OrAbove()) {
        final status = await Permission.photos.request();
        return status.isGranted;
      } else {
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    }
    
    // iOS 权限请求
    if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }

    return true; // 其他平台默认允许
  }

  /// 检查是否是Android 13+
  Future<bool> _isAndroid13OrAbove() async {
    if (!Platform.isAndroid) return false;
    
    // 获取Android SDK版本
    // 这里简化处理，实际项目中可以使用device_info_plus包
    return true; // 默认当作新版本处理
  }

  /// 检查是否有保存权限
  Future<bool> hasPermission() async {
    if (kIsWeb) return false;

    if (Platform.isAndroid) {
      if (await _isAndroid13OrAbove()) {
        return await Permission.photos.isGranted;
      } else {
        return await Permission.storage.isGranted;
      }
    }
    
    if (Platform.isIOS) {
      return await Permission.photos.isGranted;
    }

    return true;
  }

  /// 打开应用设置页面
  Future<void> openSettings() async {
    await openAppSettings();
  }
}

/// 保存结果类
class SaveResult {
  final bool success;
  final String message;
  final String? filePath;

  SaveResult._({
    required this.success,
    required this.message,
    this.filePath,
  });

  factory SaveResult.success({
    required String message,
    String? filePath,
  }) {
    return SaveResult._(
      success: true,
      message: message,
      filePath: filePath,
    );
  }

  factory SaveResult.error(String message) {
    return SaveResult._(
      success: false,
      message: message,
    );
  }
} 