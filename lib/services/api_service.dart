import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/clothing_item.dart';

class ApiService {
  static const String baseUrl = 'https://api.your-domain.com'; // 替换为实际API地址
  static const String version = 'v1';
  static const String apiBaseUrl = '$baseUrl/api/$version';
  
  // API 密钥，实际使用时应从环境变量或安全存储中获取
  static const String apiKey = 'your_api_key_here';
  
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  // 通用请求头
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $apiKey',
    'Accept': 'application/json',
    'X-Client-Version': '1.0.0',
    'X-Platform': kIsWeb ? 'web' : Platform.operatingSystem,
  };

  // 1. 用户认证相关接口
  /// 用户注册
  Future<ApiResponse<UserAuth>> register({
    required String email,
    String? username,
    String? phone,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/auth/register'),
        headers: _headers,
        body: jsonEncode({
          'email': email,
          'username': username,
          'phone': phone,
        }),
      );
      return _handleResponse<UserAuth>(response, (data) => UserAuth.fromJson(data));
    } catch (e) {
      return ApiResponse.error('注册失败: $e');
    }
  }

  /// 用户登录
  Future<ApiResponse<UserAuth>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/auth/login'),
        headers: _headers,
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );
      return _handleResponse<UserAuth>(response, (data) => UserAuth.fromJson(data));
    } catch (e) {
      return ApiResponse.error('登录失败: $e');
    }
  }

  /// 刷新Token
  Future<ApiResponse<UserAuth>> refreshToken(String refreshToken) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/auth/refresh'),
        headers: _headers,
        body: jsonEncode({'refresh_token': refreshToken}),
      );
      return _handleResponse<UserAuth>(response, (data) => UserAuth.fromJson(data));
    } catch (e) {
      return ApiResponse.error('Token刷新失败: $e');
    }
  }

  // 2. 服装相关接口
  /// 获取服装列表
  Future<ApiResponse<List<ClothingItem>>> getClothingList({
    String? category,
    String? brand,
    String? color,
    String? style,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (category != null) queryParams['category'] = category;
      if (brand != null) queryParams['brand'] = brand;
      if (color != null) queryParams['color'] = color;
      if (style != null) queryParams['style'] = style;

      final uri = Uri.parse('$apiBaseUrl/clothing').replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _headers);
      
      return _handleResponse<List<ClothingItem>>(
        response, 
        (data) => (data['items'] as List).map((item) => ClothingItem.fromJson(item)).toList(),
      );
    } catch (e) {
      return ApiResponse.error('获取服装列表失败: $e');
    }
  }

  /// 获取服装详情
  Future<ApiResponse<ClothingItem>> getClothingDetail(String clothingId) async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/clothing/$clothingId'),
        headers: _headers,
      );
      return _handleResponse<ClothingItem>(response, (data) => ClothingItem.fromJson(data));
    } catch (e) {
      return ApiResponse.error('获取服装详情失败: $e');
    }
  }

  /// 搜索服装
  Future<ApiResponse<List<ClothingItem>>> searchClothing({
    required String keyword,
    String? category,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'q': keyword,
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (category != null) queryParams['category'] = category;

      final uri = Uri.parse('$apiBaseUrl/clothing/search').replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _headers);
      
      return _handleResponse<List<ClothingItem>>(
        response, 
        (data) => (data['items'] as List).map((item) => ClothingItem.fromJson(item)).toList(),
      );
    } catch (e) {
      return ApiResponse.error('搜索服装失败: $e');
    }
  }

  // 3. 图片上传接口
  /// 上传用户照片
  Future<ApiResponse<UploadResult>> uploadUserPhoto({
    File? imageFile,
    Uint8List? imageBytes,
    required String fileName,
  }) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$apiBaseUrl/upload/user-photo'),
      );
      
      request.headers.addAll(_headers);
      request.headers.remove('Content-Type'); // Let http package set it for multipart

      if (imageFile != null) {
        request.files.add(await http.MultipartFile.fromPath('image', imageFile.path));
      } else if (imageBytes != null) {
        request.files.add(http.MultipartFile.fromBytes(
          'image',
          imageBytes,
          filename: fileName,
        ));
      } else {
        return ApiResponse.error('未提供图片数据');
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      return _handleResponse<UploadResult>(response, (data) => UploadResult.fromJson(data));
    } catch (e) {
      return ApiResponse.error('上传用户照片失败: $e');
    }
  }

  // 4. AI试衣接口
  /// 提交试衣任务
  Future<ApiResponse<TryOnTask>> submitTryOnTask({
    required String userPhotoUrl,
    required String clothingId,
    Map<String, dynamic>? options,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/tryon/submit'),
        headers: _headers,
        body: jsonEncode({
          'user_photo_url': userPhotoUrl,
          'clothing_id': clothingId,
          'options': options ?? {},
        }),
      );
      return _handleResponse<TryOnTask>(response, (data) => TryOnTask.fromJson(data));
    } catch (e) {
      return ApiResponse.error('提交试衣任务失败: $e');
    }
  }

  /// 查询任务状态
  Future<ApiResponse<TryOnTask>> getTryOnTaskStatus(String taskId) async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/tryon/task/$taskId'),
        headers: _headers,
      );
      return _handleResponse<TryOnTask>(response, (data) => TryOnTask.fromJson(data));
    } catch (e) {
      return ApiResponse.error('查询任务状态失败: $e');
    }
  }

  /// 获取试衣历史
  Future<ApiResponse<List<TryOnHistory>>> getTryOnHistory({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final uri = Uri.parse('$apiBaseUrl/tryon/history').replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _headers);
      
      return _handleResponse<List<TryOnHistory>>(
        response, 
        (data) => (data['items'] as List).map((item) => TryOnHistory.fromJson(item)).toList(),
      );
    } catch (e) {
      return ApiResponse.error('获取试衣历史失败: $e');
    }
  }

  // 5. AI推荐接口
  /// 获取个性化推荐
  Future<ApiResponse<List<ClothingRecommendation>>> getPersonalizedRecommendations({
    String? userPhotoUrl,
    String? style,
    String? occasion,
    int limit = 10,
  }) async {
    try {
      final body = <String, dynamic>{
        'limit': limit,
      };
      
      if (userPhotoUrl != null) body['user_photo_url'] = userPhotoUrl;
      if (style != null) body['style'] = style;
      if (occasion != null) body['occasion'] = occasion;

      final response = await http.post(
        Uri.parse('$apiBaseUrl/ai/recommendations'),
        headers: _headers,
        body: jsonEncode(body),
      );
      
      return _handleResponse<List<ClothingRecommendation>>(
        response, 
        (data) => (data['recommendations'] as List).map((item) => ClothingRecommendation.fromJson(item)).toList(),
      );
    } catch (e) {
      return ApiResponse.error('获取个性化推荐失败: $e');
    }
  }

  /// 分析用户风格
  Future<ApiResponse<StyleAnalysis>> analyzeUserStyle(String userPhotoUrl) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/ai/style-analysis'),
        headers: _headers,
        body: jsonEncode({'user_photo_url': userPhotoUrl}),
      );
      return _handleResponse<StyleAnalysis>(response, (data) => StyleAnalysis.fromJson(data));
    } catch (e) {
      return ApiResponse.error('分析用户风格失败: $e');
    }
  }

  // 6. 用户偏好接口
  /// 保存用户偏好
  Future<ApiResponse<bool>> saveUserPreferences(UserPreferences preferences) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/user/preferences'),
        headers: _headers,
        body: jsonEncode(preferences.toJson()),
      );
      return _handleResponse<bool>(response, (data) => data['success'] ?? false);
    } catch (e) {
      return ApiResponse.error('保存用户偏好失败: $e');
    }
  }

  /// 获取用户偏好
  Future<ApiResponse<UserPreferences>> getUserPreferences() async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/user/preferences'),
        headers: _headers,
      );
      return _handleResponse<UserPreferences>(response, (data) => UserPreferences.fromJson(data));
    } catch (e) {
      return ApiResponse.error('获取用户偏好失败: $e');
    }
  }

  // 7. 收藏和分享接口
  /// 收藏试衣结果
  Future<ApiResponse<bool>> favoriteTryOnResult(String taskId) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/user/favorites'),
        headers: _headers,
        body: jsonEncode({'task_id': taskId}),
      );
      return _handleResponse<bool>(response, (data) => data['success'] ?? false);
    } catch (e) {
      return ApiResponse.error('收藏失败: $e');
    }
  }

  /// 分享试衣结果
  Future<ApiResponse<ShareResult>> shareTryOnResult({
    required String taskId,
    required String platform,
    String? message,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/share'),
        headers: _headers,
        body: jsonEncode({
          'task_id': taskId,
          'platform': platform,
          'message': message,
        }),
      );
      return _handleResponse<ShareResult>(response, (data) => ShareResult.fromJson(data));
    } catch (e) {
      return ApiResponse.error('分享失败: $e');
    }
  }

  // 8. 系统配置接口
  /// 获取应用配置
  Future<ApiResponse<AppConfig>> getAppConfig() async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/config'),
        headers: _headers,
      );
      return _handleResponse<AppConfig>(response, (data) => AppConfig.fromJson(data));
    } catch (e) {
      return ApiResponse.error('获取应用配置失败: $e');
    }
  }

  /// 上报错误日志
  Future<ApiResponse<bool>> reportError({
    required String error,
    required String stackTrace,
    Map<String, dynamic>? context,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/system/error-report'),
        headers: _headers,
        body: jsonEncode({
          'error': error,
          'stack_trace': stackTrace,
          'context': context ?? {},
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
      return _handleResponse<bool>(response, (data) => data['success'] ?? false);
    } catch (e) {
      // 不要因为错误上报失败而影响用户体验
      return ApiResponse.error('错误上报失败');
    }
  }

  // 通用响应处理
  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    try {
      final Map<String, dynamic> data = jsonDecode(response.body);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse.success(fromJson(data));
      } else {
        final message = data['message'] ?? data['error'] ?? '请求失败';
        return ApiResponse.error(message, statusCode: response.statusCode);
      }
    } catch (e) {
      return ApiResponse.error('响应解析失败: $e', statusCode: response.statusCode);
    }
  }
}

// API响应包装类
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final int? statusCode;

  ApiResponse.success(this.data) : success = true, error = null, statusCode = null;
  ApiResponse.error(this.error, {this.statusCode}) : success = false, data = null;
}

// 数据模型类
class UserAuth {
  final String userId;
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final UserProfile profile;

  UserAuth({
    required this.userId,
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.profile,
  });

  factory UserAuth.fromJson(Map<String, dynamic> json) {
    return UserAuth(
      userId: json['user_id'],
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      expiresAt: DateTime.parse(json['expires_at']),
      profile: UserProfile.fromJson(json['profile']),
    );
  }
}

class UserProfile {
  final String? username;
  final String? email;
  final String? avatar;
  final String? gender;
  final int? age;

  UserProfile({
    this.username,
    this.email,
    this.avatar,
    this.gender,
    this.age,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      username: json['username'],
      email: json['email'],
      avatar: json['avatar'],
      gender: json['gender'],
      age: json['age'],
    );
  }
}

class UploadResult {
  final String fileId;
  final String url;
  final String fileName;
  final int fileSize;

  UploadResult({
    required this.fileId,
    required this.url,
    required this.fileName,
    required this.fileSize,
  });

  factory UploadResult.fromJson(Map<String, dynamic> json) {
    return UploadResult(
      fileId: json['file_id'],
      url: json['url'],
      fileName: json['file_name'],
      fileSize: json['file_size'],
    );
  }
}

class TryOnTask {
  final String taskId;
  final String status;
  final String? resultUrl;
  final double? progress;
  final String? error;
  final DateTime createdAt;
  final DateTime? completedAt;

  TryOnTask({
    required this.taskId,
    required this.status,
    this.resultUrl,
    this.progress,
    this.error,
    required this.createdAt,
    this.completedAt,
  });

  factory TryOnTask.fromJson(Map<String, dynamic> json) {
    return TryOnTask(
      taskId: json['task_id'],
      status: json['status'],
      resultUrl: json['result_url'],
      progress: json['progress']?.toDouble(),
      error: json['error'],
      createdAt: DateTime.parse(json['created_at']),
      completedAt: json['completed_at'] != null ? DateTime.parse(json['completed_at']) : null,
    );
  }
}

class TryOnHistory {
  final String taskId;
  final String userPhotoUrl;
  final ClothingItem clothing;
  final String? resultUrl;
  final DateTime createdAt;

  TryOnHistory({
    required this.taskId,
    required this.userPhotoUrl,
    required this.clothing,
    this.resultUrl,
    required this.createdAt,
  });

  factory TryOnHistory.fromJson(Map<String, dynamic> json) {
    return TryOnHistory(
      taskId: json['task_id'],
      userPhotoUrl: json['user_photo_url'],
      clothing: ClothingItem.fromJson(json['clothing']),
      resultUrl: json['result_url'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

class ClothingRecommendation {
  final ClothingItem clothing;
  final double score;
  final String reason;
  final List<String> tags;

  ClothingRecommendation({
    required this.clothing,
    required this.score,
    required this.reason,
    required this.tags,
  });

  factory ClothingRecommendation.fromJson(Map<String, dynamic> json) {
    return ClothingRecommendation(
      clothing: ClothingItem.fromJson(json['clothing']),
      score: json['score'].toDouble(),
      reason: json['reason'],
      tags: List<String>.from(json['tags']),
    );
  }
}

class StyleAnalysis {
  final String primaryStyle;
  final List<String> suitableStyles;
  final Map<String, double> styleScores;
  final List<String> recommendations;

  StyleAnalysis({
    required this.primaryStyle,
    required this.suitableStyles,
    required this.styleScores,
    required this.recommendations,
  });

  factory StyleAnalysis.fromJson(Map<String, dynamic> json) {
    return StyleAnalysis(
      primaryStyle: json['primary_style'],
      suitableStyles: List<String>.from(json['suitable_styles']),
      styleScores: Map<String, double>.from(json['style_scores']),
      recommendations: List<String>.from(json['recommendations']),
    );
  }
}

class UserPreferences {
  final List<String> favoriteColors;
  final List<String> favoriteStyles;
  final List<String> favoriteBrands;
  final String? preferredSize;
  final Map<String, dynamic> otherPreferences;

  UserPreferences({
    required this.favoriteColors,
    required this.favoriteStyles,
    required this.favoriteBrands,
    this.preferredSize,
    required this.otherPreferences,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      favoriteColors: List<String>.from(json['favorite_colors']),
      favoriteStyles: List<String>.from(json['favorite_styles']),
      favoriteBrands: List<String>.from(json['favorite_brands']),
      preferredSize: json['preferred_size'],
      otherPreferences: Map<String, dynamic>.from(json['other_preferences']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'favorite_colors': favoriteColors,
      'favorite_styles': favoriteStyles,
      'favorite_brands': favoriteBrands,
      'preferred_size': preferredSize,
      'other_preferences': otherPreferences,
    };
  }
}

class ShareResult {
  final String shareId;
  final String shareUrl;
  final String platform;

  ShareResult({
    required this.shareId,
    required this.shareUrl,
    required this.platform,
  });

  factory ShareResult.fromJson(Map<String, dynamic> json) {
    return ShareResult(
      shareId: json['share_id'],
      shareUrl: json['share_url'],
      platform: json['platform'],
    );
  }
}

class AppConfig {
  final String version;
  final Map<String, dynamic> features;
  final Map<String, String> endpoints;
  final Map<String, dynamic> settings;

  AppConfig({
    required this.version,
    required this.features,
    required this.endpoints,
    required this.settings,
  });

  factory AppConfig.fromJson(Map<String, dynamic> json) {
    return AppConfig(
      version: json['version'],
      features: Map<String, dynamic>.from(json['features']),
      endpoints: Map<String, String>.from(json['endpoints']),
      settings: Map<String, dynamic>.from(json['settings']),
    );
  }
} 