import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

/// 挥手检测结果
class WaveDetectionResult {
  final bool isWaving;
  final double confidence;
  final String? debugInfo;

  WaveDetectionResult({
    required this.isWaving,
    required this.confidence,
    this.debugInfo,
  });
}

/// 挥手检测服务
/// 
/// 这个服务使用简化的算法来检测挥手动作
/// 在实际项目中，建议使用 MediaPipe 或 TensorFlow Lite 等专业库
class WaveDetectionService {
  static const int _frameBufferSize = 10;
  static const double _waveThreshold = 0.6;
  static const int _consecutiveFramesRequired = 3;
  
  final List<CameraImage> _frameBuffer = [];
  final List<double> _motionHistory = [];
  bool _isProcessing = false;
  Timer? _detectionTimer;
  
  // 回调函数
  Function(WaveDetectionResult)? onWaveDetected;
  
  /// 初始化挥手检测
  void initialize({Function(WaveDetectionResult)? onWaveDetected}) {
    this.onWaveDetected = onWaveDetected;
    _startDetectionTimer();
  }
  
  /// 开始检测定时器
  void _startDetectionTimer() {
    _detectionTimer?.cancel();
    _detectionTimer = Timer.periodic(
      const Duration(milliseconds: 200), // 每200ms检测一次
      (_) => _processFrameBuffer(),
    );
  }
  
  /// 停止检测
  void dispose() {
    onWaveDetected = null;
    _detectionTimer?.cancel();
    _frameBuffer.clear();
    _motionHistory.clear();
  }
  
  /// 添加摄像头帧进行分析
  void addFrame(CameraImage image) {
    if (_isProcessing) return;
    
    _frameBuffer.add(image);
    
    // 保持缓冲区大小
    if (_frameBuffer.length > _frameBufferSize) {
      _frameBuffer.removeAt(0);
    }
  }
  
  /// 处理帧缓冲区
  void _processFrameBuffer() {
    if (_frameBuffer.length < 3 || _isProcessing) return;
    
    _isProcessing = true;
    
    try {
      final result = _detectWaveMotion();
      
      if (result.isWaving && result.confidence > _waveThreshold) {
        onWaveDetected?.call(result);
      }
      
      if (kDebugMode && result.debugInfo != null) {
        debugPrint('Wave Detection: ${result.debugInfo}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Wave detection error: $e');
      }
    } finally {
      _isProcessing = false;
    }
  }
  
  /// 检测挥手动作的核心算法
  WaveDetectionResult _detectWaveMotion() {
    if (_frameBuffer.length < 3) {
      return WaveDetectionResult(
        isWaving: false,
        confidence: 0.0,
        debugInfo: 'Insufficient frames',
      );
    }
    
    // 简化的运动检测算法
    final motionScore = _calculateMotionScore();
    _motionHistory.add(motionScore);
    
    // 保持历史记录大小
    if (_motionHistory.length > 20) {
      _motionHistory.removeAt(0);
    }
    
    // 检测挥手模式
    final wavePattern = _detectWavePattern();
    final confidence = _calculateConfidence(wavePattern, motionScore);
    
    return WaveDetectionResult(
      isWaving: wavePattern && confidence > _waveThreshold,
      confidence: confidence,
      debugInfo: 'Motion: ${motionScore.toStringAsFixed(2)}, '
                 'Pattern: $wavePattern, '
                 'Confidence: ${confidence.toStringAsFixed(2)}',
    );
  }
  
  /// 计算运动分数
  double _calculateMotionScore() {
    if (_frameBuffer.length < 2) return 0.0;
    
    try {
      final current = _frameBuffer.last;
      final previous = _frameBuffer[_frameBuffer.length - 2];
      
      // 简化的像素差异计算
      final currentBrightness = _calculateAverageBrightness(current);
      final previousBrightness = _calculateAverageBrightness(previous);
      
      final diff = (currentBrightness - previousBrightness).abs();
      
      // 归一化到0-1范围
      return (diff / 255.0).clamp(0.0, 1.0);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Motion score calculation error: $e');
      }
      return 0.0;
    }
  }
  
  /// 计算图像平均亮度
  double _calculateAverageBrightness(CameraImage image) {
    try {
      final Uint8List bytes = image.planes[0].bytes;
      int sum = 0;
      final sampleSize = (bytes.length / 100).round(); // 采样以提高性能
      
      for (int i = 0; i < bytes.length; i += sampleSize) {
        sum += bytes[i];
      }
      
      return sum / (bytes.length / sampleSize);
    } catch (e) {
      return 128.0; // 默认中等亮度
    }
  }
  
  /// 检测挥手模式
  bool _detectWavePattern() {
    if (_motionHistory.length < 6) return false;
    
    // 寻找运动的周期性模式
    final recentMotion = _motionHistory.sublist(_motionHistory.length - 6);
    
    // 检查是否有足够的运动变化
    final maxMotion = recentMotion.reduce((a, b) => a > b ? a : b);
    final minMotion = recentMotion.reduce((a, b) => a < b ? a : b);
    final motionRange = maxMotion - minMotion;
    
    // 需要有一定的运动范围
    if (motionRange < 0.1) return false;
    
    // 检查运动的连续性
    int consecutiveHighMotion = 0;
    for (final motion in recentMotion) {
      if (motion > 0.2) {
        consecutiveHighMotion++;
      } else {
        consecutiveHighMotion = 0;
      }
      
      if (consecutiveHighMotion >= _consecutiveFramesRequired) {
        return true;
      }
    }
    
    return false;
  }
  
  /// 计算置信度
  double _calculateConfidence(bool hasPattern, double motionScore) {
    double confidence = 0.0;
    
    // 基于运动分数
    confidence += motionScore * 0.4;
    
    // 基于模式检测
    if (hasPattern) {
      confidence += 0.4;
    }
    
    // 基于历史一致性
    if (_motionHistory.length >= 5) {
      final recentAverage = _motionHistory
          .sublist(_motionHistory.length - 5)
          .reduce((a, b) => a + b) / 5;
      
      if (recentAverage > 0.15) {
        confidence += 0.2;
      }
    }
    
    return confidence.clamp(0.0, 1.0);
  }
  
  /// 获取当前检测状态
  Map<String, dynamic> getDetectionStatus() {
    return {
      'isProcessing': _isProcessing,
      'frameBufferSize': _frameBuffer.length,
      'motionHistorySize': _motionHistory.length,
      'lastMotionScore': _motionHistory.isNotEmpty ? _motionHistory.last : 0.0,
    };
  }
  
  /// 重置检测状态
  void reset() {
    _frameBuffer.clear();
    _motionHistory.clear();
    _isProcessing = false;
  }
  
  /// 调整检测灵敏度
  void adjustSensitivity({
    double? waveThreshold,
    int? consecutiveFramesRequired,
  }) {
    // 这里可以动态调整检测参数
    // 实际实现中可以添加参数验证和应用逻辑
    if (kDebugMode) {
      debugPrint('Adjusting sensitivity: threshold=$waveThreshold, frames=$consecutiveFramesRequired');
    }
  }
}
