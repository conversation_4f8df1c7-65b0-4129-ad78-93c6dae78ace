import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class MirrorResult {
  final bool success;
  final String message;
  final String? data;

  MirrorResult({
    required this.success,
    required this.message,
    this.data,
  });

  factory MirrorResult.success(String message, {String? data}) {
    return MirrorResult(
      success: true,
      message: message,
      data: data,
    );
  }

  factory MirrorResult.error(String message) {
    return MirrorResult(
      success: false,
      message: message,
    );
  }
}

class MirrorService {
  static const String _storageFolder = 'smart_mirror_photos';

  /// 生成二维码供用户扫描获取照片
  Future<MirrorResult> generateQRCode({
    String? imagePath,
    Uint8List? imageBytes,
    String? userId,
  }) async {
    try {
      // 1. 先保存图片到本地
      final savedPath = await _saveToLocal(
        imagePath: imagePath,
        imageBytes: imageBytes,
        userId: userId,
      );

      if (savedPath == null) {
        return MirrorResult.error('保存图片失败');
      }

      // 2. 生成访问链接 (实际项目中应该是云端链接)
      final imageId = path.basenameWithoutExtension(savedPath);
      final shareUrl = 'https://mirror.example.com/share/$imageId';

      // 3. 返回二维码内容 (实际项目中可以集成二维码生成库)
      return MirrorResult.success(
        '二维码已生成，用户可扫码获取照片',
        data: shareUrl,
      );
    } catch (e) {
      return MirrorResult.error('生成二维码失败: $e');
    }
  }

  /// 发送到用户邮箱
  Future<MirrorResult> sendToEmail({
    required String email,
    String? imagePath,
    Uint8List? imageBytes,
    String? message,
  }) async {
    try {
      // 保存图片到本地 (实际项目中需要集成邮件发送服务)
      final savedPath = await _saveToLocal(
        imagePath: imagePath,
        imageBytes: imageBytes,
      );

      if (savedPath == null) {
        return MirrorResult.error('保存图片失败');
      }

      // 模拟邮件发送 (实际项目中集成SMTP服务)
      await Future.delayed(const Duration(seconds: 1));

      return MirrorResult.success(
        '照片已发送到 $email',
        data: savedPath,
      );
    } catch (e) {
      return MirrorResult.error('邮件发送失败: $e');
    }
  }

  /// 保存到镜子本地存储
  Future<MirrorResult> saveToLocal({
    String? imagePath,
    Uint8List? imageBytes,
    String? userId,
    String? customName,
  }) async {
    try {
      final savedPath = await _saveToLocal(
        imagePath: imagePath,
        imageBytes: imageBytes,
        userId: userId,
        customName: customName,
      );

      if (savedPath == null) {
        return MirrorResult.error('保存失败');
      }

      return MirrorResult.success(
        '已保存到镜子本地存储',
        data: savedPath,
      );
    } catch (e) {
      return MirrorResult.error('保存失败: $e');
    }
  }

  /// 蓝牙传输到手机 (需要蓝牙权限和配对)
  Future<MirrorResult> sendViaBluetooth({
    String? imagePath,
    Uint8List? imageBytes,
    String? deviceName,
  }) async {
    try {
      // 检查图片是否可用
      if (imagePath == null && imageBytes == null) {
        return MirrorResult.error('没有可传输的图片');
      }

      // 模拟蓝牙传输 (实际项目中需要集成蓝牙库)
      await Future.delayed(const Duration(seconds: 2));

      return MirrorResult.success(
        '照片已通过蓝牙发送${deviceName != null ? '到 $deviceName' : ''}',
      );
    } catch (e) {
      return MirrorResult.error('蓝牙传输失败: $e');
    }
  }

  /// 获取本地存储的照片列表
  Future<List<String>> getLocalPhotos({String? userId}) async {
    try {
      Directory? directory;
      
      if (kIsWeb) {
        // Web环境下使用浏览器存储
        return [];
      } else {
        // 移动端获取应用文档目录
        final appDocDir = await getApplicationDocumentsDirectory();
        directory = Directory(path.join(appDocDir.path, _storageFolder));
      }

      if (!await directory.exists()) {
        return [];
      }

      final files = await directory.list().toList();
      final imageFiles = files
          .whereType<File>()
          .map((file) => file.path)
          .where((filePath) => _isImageFile(filePath))
          .toList();

      // 如果指定了用户ID，过滤该用户的照片
      if (userId != null) {
        return imageFiles
            .where((filePath) => path.basename(filePath).startsWith('${userId}_'))
            .toList();
      }

      return imageFiles;
    } catch (e) {
      if (kDebugMode) {
        print('获取本地照片列表失败: $e');
      }
      return [];
    }
  }

  /// 删除本地照片
  Future<MirrorResult> deleteLocalPhoto(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        return MirrorResult.success('照片已删除');
      } else {
        return MirrorResult.error('照片不存在');
      }
    } catch (e) {
      return MirrorResult.error('删除失败: $e');
    }
  }

  /// 清理过期照片 (超过指定天数的照片)
  Future<MirrorResult> cleanupExpiredPhotos({int daysOld = 7}) async {
    try {
      final photos = await getLocalPhotos();
      final now = DateTime.now();
      int deletedCount = 0;

      for (final photoPath in photos) {
        final file = File(photoPath);
        final fileStat = await file.stat();
        final daysDiff = now.difference(fileStat.modified).inDays;

        if (daysDiff > daysOld) {
          await file.delete();
          deletedCount++;
        }
      }

      return MirrorResult.success('清理完成，删除了 $deletedCount 张过期照片');
    } catch (e) {
      return MirrorResult.error('清理失败: $e');
    }
  }

  /// 私有方法：保存图片到本地
  Future<String?> _saveToLocal({
    String? imagePath,
    Uint8List? imageBytes,
    String? userId,
    String? customName,
  }) async {
    try {
      Directory? directory;
      
      if (kIsWeb) {
        // Web环境下无法直接保存到文件系统
        return null;
      } else {
        // 移动端获取应用文档目录
        final appDocDir = await getApplicationDocumentsDirectory();
        directory = Directory(path.join(appDocDir.path, _storageFolder));
      }

      // 确保目录存在
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // 生成文件名
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final userPrefix = userId != null ? '${userId}_' : '';
      final fileName = customName ?? '${userPrefix}tryon_$timestamp.jpg';
      final filePath = path.join(directory.path, fileName);

      // 保存文件
      final file = File(filePath);
      
      if (imageBytes != null) {
        await file.writeAsBytes(imageBytes);
      } else if (imagePath != null) {
        final sourceFile = File(imagePath);
        await sourceFile.copy(filePath);
      } else {
        return null;
      }

      return filePath;
    } catch (e) {
      if (kDebugMode) {
        print('保存图片到本地失败: $e');
      }
      return null;
    }
  }

  /// 检查是否为图片文件
  bool _isImageFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(extension);
  }
} 