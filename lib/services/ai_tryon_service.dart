import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/clothing_item.dart';

class AITryOnResult {
  final bool success;
  final String message;
  final String? resultImageUrl;
  final Uint8List? resultImageBytes;
  final String? taskId;
  final double? confidence;

  AITryOnResult({
    required this.success,
    required this.message,
    this.resultImageUrl,
    this.resultImageBytes,
    this.taskId,
    this.confidence,
  });

  factory AITryOnResult.success({
    required String message,
    String? resultImageUrl,
    Uint8List? resultImageBytes,
    String? taskId,
    double? confidence,
  }) {
    return AITryOnResult(
      success: true,
      message: message,
      resultImageUrl: resultImageUrl,
      resultImageBytes: resultImageBytes,
      taskId: taskId,
      confidence: confidence,
    );
  }

  factory AITryOnResult.error(String message) {
    return AITryOnResult(
      success: false,
      message: message,
    );
  }
}

class AITryOnService {
  // 阿里云OutfitAnyone API配置
  static const String _baseUrl = 'https://outfitanyone.alibabacloud.com/api/v1';
  static const String _accessKey = 'your_access_key_here'; // 需要替换为实际的访问密钥
  static const String _secretKey = 'your_secret_key_here'; // 需要替换为实际的秘密密钥
  
  static final AITryOnService _instance = AITryOnService._internal();
  factory AITryOnService() => _instance;
  AITryOnService._internal();

  /// 处理AI试衣请求
  Future<AITryOnResult> processVirtualTryOn({
    required String userImagePath,
    required Uint8List? userImageBytes,
    required ClothingItem clothing,
    Map<String, dynamic>? options,
  }) async {
    try {
      // 检查参数
      if (userImagePath.isEmpty && userImageBytes == null) {
        return AITryOnResult.error('用户照片数据不能为空');
      }

      // 1. 上传用户照片
      final userPhotoUploadResult = await _uploadUserPhoto(
        imagePath: userImagePath,
        imageBytes: userImageBytes,
      );

      if (!userPhotoUploadResult.success) {
        return AITryOnResult.error('上传用户照片失败: ${userPhotoUploadResult.message}');
      }

      // 2. 提交试衣任务
      final taskResult = await _submitTryOnTask(
        userPhotoUrl: userPhotoUploadResult.data!,
        clothingId: clothing.id,
        options: options,
      );

      if (!taskResult.success) {
        return AITryOnResult.error('提交试衣任务失败: ${taskResult.message}');
      }

      // 3. 轮询任务状态直到完成
      final finalResult = await _pollTaskStatus(taskResult.data!);
      
      return finalResult;
    } catch (e) {
      return AITryOnResult.error('AI试衣处理失败: $e');
    }
  }

  /// 上传用户照片到阿里云
  Future<_UploadResult> _uploadUserPhoto({
    String? imagePath,
    Uint8List? imageBytes,
  }) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/photos/upload'),
      );

      // 添加认证头
      request.headers.addAll(_getAuthHeaders());

      // 添加图片文件
      if (imageBytes != null) {
        request.files.add(http.MultipartFile.fromBytes(
          'image',
          imageBytes,
          filename: 'user_photo_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ));
      } else if (imagePath != null && imagePath.isNotEmpty) {
        if (kIsWeb) {
          return _UploadResult.error('Web环境下需要提供图片字节数据');
        }
        
        final file = File(imagePath);
        if (!await file.exists()) {
          return _UploadResult.error('图片文件不存在');
        }

        request.files.add(await http.MultipartFile.fromPath(
          'image',
          imagePath,
          filename: 'user_photo_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ));
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return _UploadResult.success(data['photo_url']);
        } else {
          return _UploadResult.error(data['message'] ?? '上传失败');
        }
      } else {
        return _UploadResult.error('上传失败，状态码: ${response.statusCode}');
      }
    } catch (e) {
      return _UploadResult.error('上传异常: $e');
    }
  }

  /// 提交试衣任务
  Future<_TaskResult> _submitTryOnTask({
    required String userPhotoUrl,
    required String clothingId,
    Map<String, dynamic>? options,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/tryon/submit'),
        headers: _getAuthHeaders(),
        body: jsonEncode({
          'user_photo_url': userPhotoUrl,
          'clothing_id': clothingId,
          'model_type': 'outfitanyone_v2', // 使用最新版本
          'quality': 'high', // 高质量输出
          'options': {
            'preserve_face': true, // 保持面部特征
            'fit_clothing': true, // 自动调整服装尺寸
            'style_transfer': false, // 不进行风格转换
            ...?options,
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return _TaskResult.success(data['task_id']);
        } else {
          return _TaskResult.error(data['message'] ?? '提交任务失败');
        }
      } else {
        return _TaskResult.error('提交失败，状态码: ${response.statusCode}');
      }
    } catch (e) {
      return _TaskResult.error('提交异常: $e');
    }
  }

  /// 轮询任务状态
  Future<AITryOnResult> _pollTaskStatus(String taskId) async {
    const maxPollingAttempts = 30; // 最多轮询30次
    const pollingInterval = Duration(seconds: 2); // 每2秒轮询一次

    for (int attempt = 0; attempt < maxPollingAttempts; attempt++) {
      try {
        final response = await http.get(
          Uri.parse('$_baseUrl/tryon/status/$taskId'),
          headers: _getAuthHeaders(),
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          
          if (data['success'] == true) {
            final status = data['status'];
            
            switch (status) {
              case 'completed':
                // 任务完成，下载结果图片
                final resultImageUrl = data['result_url'];
                final confidence = (data['confidence'] as num?)?.toDouble();
                
                final imageBytes = await _downloadResultImage(resultImageUrl);
                
                return AITryOnResult.success(
                  message: '试衣完成',
                  resultImageUrl: resultImageUrl,
                  resultImageBytes: imageBytes,
                  taskId: taskId,
                  confidence: confidence,
                );
                
              case 'failed':
                return AITryOnResult.error(
                  data['error_message'] ?? '试衣任务失败'
                );
                
              case 'processing':
                // 继续轮询
                break;
                
              default:
                // 未知状态，继续轮询
                break;
            }
          } else {
            return AITryOnResult.error(data['message'] ?? '查询任务状态失败');
          }
        } else {
          return AITryOnResult.error('查询失败，状态码: ${response.statusCode}');
        }
        
        // 等待一段时间后继续轮询
        if (attempt < maxPollingAttempts - 1) {
          await Future.delayed(pollingInterval);
        }
      } catch (e) {
        return AITryOnResult.error('查询异常: $e');
      }
    }

    return AITryOnResult.error('任务处理超时，请稍后重试');
  }

  /// 下载结果图片
  Future<Uint8List?> _downloadResultImage(String imageUrl) async {
    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        return response.bodyBytes;
      }
    } catch (e) {
      if (kDebugMode) {
        print('下载结果图片失败: $e');
      }
    }
    return null;
  }

  /// 获取认证头信息
  Map<String, String> _getAuthHeaders() {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    
    // 实际项目中应该实现正确的签名算法
    // 这里简化处理，实际使用阿里云SDK的签名方法
    final signature = _generateSignature(timestamp);
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_accessKey',
      'X-Timestamp': timestamp,
      'X-Signature': signature,
      'X-Client-Version': '1.0.0',
      'X-Platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
  }

  /// 生成API签名 (简化版本)
  String _generateSignature(String timestamp) {
    // 实际项目中应该使用阿里云的签名算法
    // 这里为演示目的简化处理
    final data = '$_accessKey$timestamp$_secretKey';
    return data.hashCode.toString();
  }

  /// 批量试衣 (同时尝试多件服装)
  Future<List<AITryOnResult>> batchTryOn({
    required String userImagePath,
    required Uint8List? userImageBytes,
    required List<ClothingItem> clothingItems,
    Map<String, dynamic>? options,
  }) async {
    final results = <AITryOnResult>[];
    
    for (final clothing in clothingItems) {
      final result = await processVirtualTryOn(
        userImagePath: userImagePath,
        userImageBytes: userImageBytes,
        clothing: clothing,
        options: options,
      );
      results.add(result);
      
      // 添加延迟避免API频率限制
      await Future.delayed(const Duration(milliseconds: 500));
    }
    
    return results;
  }

  /// 获取AI试衣建议
  Future<AITryOnResult> getStyleSuggestions({
    required String userImagePath,
    required Uint8List? userImageBytes,
    String? occasion,
    String? style,
  }) async {
    try {
      // 上传用户照片进行分析
      final uploadResult = await _uploadUserPhoto(
        imagePath: userImagePath,
        imageBytes: userImageBytes,
      );

      if (!uploadResult.success) {
        return AITryOnResult.error('上传照片失败: ${uploadResult.message}');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/ai/suggestions'),
        headers: _getAuthHeaders(),
        body: jsonEncode({
          'user_photo_url': uploadResult.data,
          'occasion': occasion,
          'style': style,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return AITryOnResult.success(
            message: '获取AI建议成功',
            taskId: data['suggestion_id'],
          );
        } else {
          return AITryOnResult.error(data['message'] ?? '获取建议失败');
        }
      } else {
        return AITryOnResult.error('请求失败，状态码: ${response.statusCode}');
      }
    } catch (e) {
      return AITryOnResult.error('获取AI建议异常: $e');
    }
  }
}

// 辅助类
class _UploadResult {
  final bool success;
  final String message;
  final String? data;

  _UploadResult({
    required this.success,
    required this.message,
    this.data,
  });

  factory _UploadResult.success(String data) {
    return _UploadResult(
      success: true,
      message: '上传成功',
      data: data,
    );
  }

  factory _UploadResult.error(String message) {
    return _UploadResult(
      success: false,
      message: message,
    );
  }
}

class _TaskResult {
  final bool success;
  final String message;
  final String? data;

  _TaskResult({
    required this.success,
    required this.message,
    this.data,
  });

  factory _TaskResult.success(String data) {
    return _TaskResult(
      success: true,
      message: '提交成功',
      data: data,
    );
  }

  factory _TaskResult.error(String message) {
    return _TaskResult(
      success: false,
      message: message,
    );
  }
} 