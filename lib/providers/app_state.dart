import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';
import '../models/clothing_item.dart';
import '../data/sample_clothing.dart';
import '../services/gallery_service.dart';
import '../services/mirror_service.dart';

enum AppScreen {
  camera,
  photoConfirm,
  clothingSelection,
  processing,
  result,
  error,
}

class AppState extends ChangeNotifier {
  AppScreen _currentScreen = AppScreen.camera;
  CameraController? _cameraController;
  List<CameraDescription> _cameras = [];
  int _selectedCameraIndex = 0;
  File? _capturedPhoto;
  Uint8List? _capturedPhotoBytes;
  String _selectedCategory = ClothingCategory.tops;
  ClothingItem? _selectedClothing;
  bool _isProcessing = false;
  String? _errorMessage;
  File? _resultImage;
  Uint8List? _resultImageBytes;

  // Getters
  AppScreen get currentScreen => _currentScreen;
  CameraController? get cameraController => _cameraController;
  List<CameraDescription> get cameras => _cameras;
  int get selectedCameraIndex => _selectedCameraIndex;
  File? get capturedPhoto => _capturedPhoto;
  Uint8List? get capturedPhotoBytes => _capturedPhotoBytes;
  String get selectedCategory => _selectedCategory;
  ClothingItem? get selectedClothing => _selectedClothing;
  bool get isProcessing => _isProcessing;
  String? get errorMessage => _errorMessage;
  File? get resultImage => _resultImage;
  Uint8List? get resultImageBytes => _resultImageBytes;

  // Sample clothing data
  final List<ClothingItem> _clothingItems = SampleClothing.getAllClothing();

  List<ClothingItem> get clothingItems => _clothingItems
      .where((item) => item.category == _selectedCategory)
      .toList();

  // 获取指定分类的服装
  List<ClothingItem> getClothingByCategory(String category) {
    return _clothingItems.where((item) => item.category == category).toList();
  }

  // Initialize cameras
  Future<void> initializeCameras() async {
    try {
      _cameras = await availableCameras();
      if (_cameras.isNotEmpty) {
        await _initializeCamera(_selectedCameraIndex);
      }
    } catch (e) {
      _errorMessage = '相机初始化失败: $e';
      notifyListeners();
    }
  }

  Future<void> _initializeCamera(int cameraIndex) async {
    if (_cameraController != null) {
      await _cameraController!.dispose();
    }

    _cameraController = CameraController(
      _cameras[cameraIndex],
      ResolutionPreset.high,
      enableAudio: false,
    );

    try {
      await _cameraController!.initialize();
      notifyListeners();
    } catch (e) {
      _errorMessage = '相机初始化失败: $e';
      notifyListeners();
    }
  }

  // Switch camera
  Future<void> switchCamera() async {
    if (_cameras.length > 1) {
      _selectedCameraIndex = (_selectedCameraIndex + 1) % _cameras.length;
      await _initializeCamera(_selectedCameraIndex);
    }
  }

  // Take picture
  Future<void> takePicture() async {
    if (_cameraController != null && _cameraController!.value.isInitialized) {
      try {
        final XFile photo = await _cameraController!.takePicture();
        if (kIsWeb) {
          // 在Web平台上，读取图片字节数据
          _capturedPhotoBytes = await photo.readAsBytes();
        } else {
          // 在移动平台上，使用File
          _capturedPhoto = File(photo.path);
        }
        _currentScreen = AppScreen.photoConfirm;
        notifyListeners();
      } catch (e) {
        _errorMessage = '拍照失败: $e';
        _currentScreen = AppScreen.error;
        notifyListeners();
      }
    }
  }

  // Navigation methods
  Future<void> showCameraScreen() async {
    _currentScreen = AppScreen.camera;
    _capturedPhoto = null;
    _capturedPhotoBytes = null;
    _selectedClothing = null;
    _resultImage = null;
    _resultImageBytes = null;
    _errorMessage = null;
    
    // 重新初始化相机以确保正常工作
    if (_cameras.isNotEmpty && _cameraController != null) {
      try {
        await _initializeCamera(_selectedCameraIndex);
      } catch (e) {
        _errorMessage = '相机重新初始化失败: $e';
        _currentScreen = AppScreen.error;
      }
    }
    
    notifyListeners();
  }

  void confirmPhoto() {
    _currentScreen = AppScreen.clothingSelection;
    notifyListeners();
  }

  void retakePicture() {
    _capturedPhoto = null;
    _capturedPhotoBytes = null;
    _currentScreen = AppScreen.camera;
    notifyListeners();
  }

  void showClothingSelection() {
    _currentScreen = AppScreen.clothingSelection;
    notifyListeners();
  }
  
  // 镜子模式专用导航方法 - 简化版相机屏幕切换
  void showMirrorCameraScreen() {
    _currentScreen = AppScreen.camera;
    notifyListeners();
  }
  
  void showClothingSelectionScreen() {
    _currentScreen = AppScreen.clothingSelection;
    notifyListeners();
  }
  
  void showProcessingScreen() {
    _currentScreen = AppScreen.processing;
    notifyListeners();
  }
  
  void showResultScreen() {
    _currentScreen = AppScreen.result;
    // 进入结果页面时关闭摄像头以节省资源
    _disposeCameraForResult();
    notifyListeners();
  }
  
  // 为结果页面释放摄像头资源
  Future<void> _disposeCameraForResult() async {
    if (_cameraController != null) {
      try {
        await _cameraController!.dispose();
        _cameraController = null;
      } catch (e) {
        if (kDebugMode) print('释放摄像头失败: $e');
      }
    }
  }
  
  // 拍照功能（用于镜子模式）
  Future<void> capturePhoto() async {
    await takePicture();
    if (_capturedPhoto != null || _capturedPhotoBytes != null) {
      _currentScreen = AppScreen.photoConfirm;
      notifyListeners();
    }
  }

  void selectCategory(String category) {
    _selectedCategory = category;
    _selectedClothing = null;
    notifyListeners();
  }

  void selectClothing(ClothingItem clothing) {
    _selectedClothing = clothing;
    notifyListeners();
  }

  Future<void> startProcessing() async {
    if (_selectedClothing == null || (_capturedPhoto == null && _capturedPhotoBytes == null)) return;

    _currentScreen = AppScreen.processing;
    _isProcessing = true;
    notifyListeners();

    // Simulate AI processing
    await Future.delayed(const Duration(seconds: 3));

    // For demo purposes, we'll just use the original photo as result
    if (kIsWeb) {
      _resultImageBytes = _capturedPhotoBytes;
    } else {
      _resultImage = _capturedPhoto;
    }
    _isProcessing = false;
    _currentScreen = AppScreen.result;
    notifyListeners();
  }

  void showError(String message) {
    _errorMessage = message;
    _currentScreen = AppScreen.error;
    notifyListeners();
  }

  void retryProcessing() {
    _errorMessage = null;
    startProcessing();
  }
  
  // 保存到相册
  Future<SaveResult> saveToGallery() async {
    try {
      final galleryService = GalleryService();
      
      if (kIsWeb) {
        // Web环境使用字节数据
        if (_resultImageBytes != null) {
          return await galleryService.saveImageToGallery(
            imageBytes: _resultImageBytes,
            fileName: 'virtual_tryon_${DateTime.now().millisecondsSinceEpoch}',
          );
        }
      } else {
        // 移动端优先使用文件路径
        if (_resultImage != null) {
          return await galleryService.saveImageToGallery(
            imagePath: _resultImage!.path,
            fileName: 'virtual_tryon_${DateTime.now().millisecondsSinceEpoch}',
          );
        }
      }
      
      return SaveResult.error('没有试衣结果可保存');
    } catch (e) {
      return SaveResult.error('保存失败: $e');
    }
  }

  // 镜子专用功能
  Future<MirrorResult> generateQRCode() async {
    try {
      final mirrorService = MirrorService();
      
      if (kIsWeb) {
        return await mirrorService.generateQRCode(imageBytes: _resultImageBytes);
      } else {
        return await mirrorService.generateQRCode(imagePath: _resultImage?.path);
      }
    } catch (e) {
      return MirrorResult.error('生成二维码失败: $e');
    }
  }

  Future<MirrorResult> sendToEmail(String email) async {
    try {
      final mirrorService = MirrorService();
      
      if (kIsWeb) {
        return await mirrorService.sendToEmail(
          email: email,
          imageBytes: _resultImageBytes,
        );
      } else {
        return await mirrorService.sendToEmail(
          email: email,
          imagePath: _resultImage?.path,
        );
      }
    } catch (e) {
      return MirrorResult.error('邮件发送失败: $e');
    }
  }

  Future<MirrorResult> saveToMirrorLocal() async {
    try {
      final mirrorService = MirrorService();
      
      if (kIsWeb) {
        return await mirrorService.saveToLocal(imageBytes: _resultImageBytes);
      } else {
        return await mirrorService.saveToLocal(imagePath: _resultImage?.path);
      }
    } catch (e) {
      return MirrorResult.error('本地保存失败: $e');
    }
  }

  Future<MirrorResult> sendViaBluetooth({String? deviceName}) async {
    try {
      final mirrorService = MirrorService();
      
      if (kIsWeb) {
        return await mirrorService.sendViaBluetooth(
          imageBytes: _resultImageBytes,
          deviceName: deviceName,
        );
      } else {
        return await mirrorService.sendViaBluetooth(
          imagePath: _resultImage?.path,
          deviceName: deviceName,
        );
      }
    } catch (e) {
      return MirrorResult.error('蓝牙传输失败: $e');
    }
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }
}