import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';
import '../providers/app_state.dart';
import '../widgets/mirror_overlay_ui.dart';
import '../widgets/gesture_detector_widget.dart';

class MirrorCameraScreen extends StatefulWidget {
  const MirrorCameraScreen({super.key});

  @override
  State<MirrorCameraScreen> createState() => _MirrorCameraScreenState();
}

class _MirrorCameraScreenState extends State<MirrorCameraScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final cameraController = appState.cameraController;
        
        if (cameraController == null || !cameraController.value.isInitialized) {
          return _buildLoadingScreen();
        }

        return Scaffold(
          body: Stack(
            children: [
              // 全屏相机预览 - 镜子效果
              _buildMirrorView(cameraController),
              
              // 智能手势检测层
              GestureDetectorWidget(
                onSwipeLeft: () => _handleSwipeLeft(appState),
                onSwipeRight: () => _handleSwipeRight(appState),
                onDoubleTap: () => _handleDoubleTap(appState),
                onLongPress: () => _handleLongPress(appState),
                child: Container(),
              ),
              
              // 镜子覆盖UI - 透明悬浮元素
              FadeTransition(
                opacity: _fadeAnimation,
                child: MirrorOverlayUI(
                  onClothingTap: () => _showClothingSelection(appState),
                  onCaptureTap: () => _capturePhoto(appState),
                  onSettingsTap: () => _showSettings(appState),
                ),
              ),
              
              // 用户接近提示
              _buildProximityHint(),
              
              // Web演示信息
              _buildWebDemoInfo(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMirrorView(CameraController controller) {
    return Positioned.fill(
      child: Transform.scale(
        scale: 1.2, // 稍微放大以实现全屏效果
        child: Center(
          child: AspectRatio(
            aspectRatio: controller.value.aspectRatio,
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationY(3.14159), // 镜像翻转
              child: CameraPreview(controller),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.5,
            colors: [
              Color(0xFF2A2A2A),
              Color(0xFF1A1A1A),
              Color(0xFF000000),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 镜子边框效果
              Container(
                width: 200,
                height: 300,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.1),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                ),
              ),
              const SizedBox(height: 30),
              Text(
                '智能镜子正在启动...',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 18,
                  fontWeight: FontWeight.w300,
                  letterSpacing: 1.2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProximityHint() {
    return Positioned(
      top: 150, // 调整位置避免与Web演示信息重叠
      left: 0,
      right: 0,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.waving_hand,
                      color: Colors.white.withValues(alpha: 0.9),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '挥手开始试衣',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 手势处理方法
  void _handleSwipeLeft(AppState appState) {
    // 向左滑动 - 切换到服装选择
    appState.showClothingSelectionScreen();
  }

  void _handleSwipeRight(AppState appState) {
    // 向右滑动 - 切换相机
    appState.switchCamera();
  }

  void _handleDoubleTap(AppState appState) {
    // 双击 - 拍照
    _capturePhoto(appState);
  }

  void _handleLongPress(AppState appState) {
    // 长按 - 显示设置
    _showSettings(appState);
  }

  void _showClothingSelection(AppState appState) {
    appState.showClothingSelectionScreen();
  }

  void _capturePhoto(AppState appState) async {
    final currentContext = context;
    try {
      await appState.capturePhoto();
    } catch (e) {
      // 处理拍照错误
      if (currentContext.mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          SnackBar(
            content: Text('拍照失败: $e'),
            backgroundColor: Colors.red.withValues(alpha: 0.8),
          ),
        );
      }
    }
  }

  void _showSettings(AppState appState) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSettingsSheet(),
    );
  }

  Widget _buildSettingsSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6, // 60%屏幕高度
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF2A2A2A),
            Color(0xFF1A1A1A),
          ],
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // 标题
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.settings,
                color: Colors.white.withValues(alpha: 0.8),
                size: 24,
              ),
              const SizedBox(width: 10),
              Text(
                '智能镜子设置',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 30),
          
          // 设置选项列表
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  _buildSettingItem(
                    icon: Icons.camera_alt,
                    title: '相机设置',
                    subtitle: '调整拍摄参数',
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.flip,
                    title: '镜像翻转',
                    subtitle: '启用镜子效果',
                    onTap: () {},
                    hasSwitch: true,
                    switchValue: true,
                  ),
                  _buildSettingItem(
                    icon: Icons.gesture,
                    title: '手势控制',
                    subtitle: '配置手势操作',
                    onTap: () {},
                    hasSwitch: true,
                    switchValue: true,
                  ),
                  _buildSettingItem(
                    icon: Icons.auto_awesome,
                    title: 'AI助手',
                    subtitle: '智能建议和语音控制',
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.tune,
                    title: '显示效果',
                    subtitle: '亮度、对比度、滤镜',
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.info_outline,
                    title: '关于镜子',
                    subtitle: '版本信息和帮助',
                    onTap: () {},
                  ),
                ],
              ),
            ),
          ),
          
          // 底部按钮
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white.withValues(alpha: 0.1),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: const Text('完成'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool hasSwitch = false,
    bool switchValue = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: hasSwitch ? null : onTap,
          borderRadius: BorderRadius.circular(15),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 45,
                  height: 45,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white.withValues(alpha: 0.8),
                    size: 22,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.6),
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                if (hasSwitch)
                  Switch(
                    value: switchValue,
                    onChanged: (value) {},
                    activeColor: Colors.cyan,
                    inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
                    inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
                  )
                else
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white.withValues(alpha: 0.4),
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }





  Widget _buildWebDemoInfo() {
    return Positioned(
      top: 50,
      left: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.cyan.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.auto_awesome,
              color: Colors.cyan.withValues(alpha: 0.8),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              '智能试衣镜界面 - 镜子模式',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 