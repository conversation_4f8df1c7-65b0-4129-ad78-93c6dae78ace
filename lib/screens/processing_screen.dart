import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:provider/provider.dart';
import '../providers/app_state.dart';

class ProcessingScreen extends StatefulWidget {
  const ProcessingScreen({super.key});

  @override
  State<ProcessingScreen> createState() => _ProcessingScreenState();
}

class _ProcessingScreenState extends State<ProcessingScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _tipController;
  late Animation<double> _progressAnimation;
  int _currentTipIndex = 0;
  
  final List<ProcessingTip> _tips = [
    ProcessingTip(
      icon: Icons.auto_awesome,
      text: '正在分析您的体型特征',
      color: Color(0xFF667eea),
    ),
    ProcessingTip(
      icon: Icons.palette,
      text: '正在匹配衣服尺寸',
      color: Color(0xFF764ba2),
    ),
    ProcessingTip(
      icon: Icons.image,
      text: '正在生成试衣效果',
      color: Color(0xFF48bb78),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _tipController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
    
    _startAnimations();
  }
  
  void _startAnimations() {
    _progressController.forward();
    _cycleTips();
  }
  
  void _cycleTips() async {
    for (int i = 0; i < _tips.length; i++) {
      setState(() {
        _currentTipIndex = i;
      });
      _tipController.reset();
      _tipController.forward();
      await Future.delayed(const Duration(milliseconds: 1000));
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _tipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF667eea),
                  Color(0xFF764ba2),
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(30),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(),
                    
                    // Main loading animation
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(60),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 2,
                        ),
                      ),
                      child: const Center(
                        child: SpinKitRing(
                          color: Colors.white,
                          size: 60,
                          lineWidth: 4,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // Title
                    const Text(
                      'AI正在为您试衣',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Subtitle
                    Text(
                      '请稍候，这可能需要几秒钟...',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 16,
                      ),
                    ),
                    
                    const SizedBox(height: 50),
                    
                    // Progress bar
                    Container(
                      width: double.infinity,
                      height: 6,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: AnimatedBuilder(
                        animation: _progressAnimation,
                        builder: (context, child) {
                          return FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: _progressAnimation.value,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(3),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.white.withValues(alpha: 0.5),
                                    blurRadius: 8,
                                    offset: const Offset(0, 0),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // Processing tips
                    Flexible(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: _tips.asMap().entries.map((entry) {
                          final index = entry.key;
                          final tip = entry.value;
                          final isActive = index == _currentTipIndex;
                          final isPassed = index < _currentTipIndex;
                          
                          return AnimatedBuilder(
                            animation: _tipController,
                            builder: (context, child) {
                              final opacity = isActive 
                                  ? _tipController.value
                                  : isPassed 
                                      ? 1.0 
                                      : 0.3;
                              
                              return AnimatedOpacity(
                                opacity: opacity,
                                duration: const Duration(milliseconds: 300),
                                child: Container(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: isActive || isPassed
                                              ? Colors.white
                                              : Colors.white.withValues(alpha: 0.3),
                                          borderRadius: BorderRadius.circular(20),
                                        ),
                                        child: Icon(
                                          isPassed ? Icons.check : tip.icon,
                                          color: isActive || isPassed
                                              ? tip.color
                                              : Colors.white.withValues(alpha: 0.6),
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Text(
                                          tip.text,
                                          style: TextStyle(
                                            color: isActive || isPassed
                                                ? Colors.white
                                                : Colors.white.withValues(alpha: 0.6),
                                            fontSize: 16,
                                            fontWeight: isActive
                                                ? FontWeight.w600
                                                : FontWeight.w400,
                                          ),
                                        ),
                                      ),
                                      if (isActive)
                                        const SpinKitThreeBounce(
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        }).toList(),
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Cancel button (optional)
                    TextButton(
                      onPressed: () {
                        appState.showClothingSelection();
                      },
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class ProcessingTip {
  final IconData icon;
  final String text;
  final Color color;
  
  ProcessingTip({
    required this.icon,
    required this.text,
    required this.color,
  });
}