import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state.dart';

class MirrorProcessingScreen extends StatefulWidget {
  const MirrorProcessingScreen({super.key});

  @override
  State<MirrorProcessingScreen> createState() => _MirrorProcessingScreenState();
}

class _MirrorProcessingScreenState extends State<MirrorProcessingScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scanController;
  late AnimationController _particleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scanAnimation;
  late Animation<double> _particleAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startProcessing();
  }

  void _initAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scanController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scanAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scanController,
      curve: Curves.easeInOut,
    ));

    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.linear,
    ));

    // 启动动画
    _pulseController.repeat(reverse: true);
    _scanController.repeat();
    _particleController.repeat();
  }

  void _startProcessing() {
    // 模拟AI处理过程
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        final appState = Provider.of<AppState>(context, listen: false);
        appState.showResultScreen();
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scanController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 1.5,
                colors: [
                  Color(0xFF1A1A2E),
                  Color(0xFF0F0F23),
                  Color(0xFF000000),
                ],
              ),
            ),
            child: Stack(
              children: [
                // 背景粒子效果
                _buildParticleBackground(),
                
                // 主要内容区域
                _buildMainContent(appState),
                
                // 扫描线效果
                _buildScanLineEffect(),
                
                // 顶部状态栏
                _buildTopStatusBar(),
                
                // 底部进度信息
                _buildBottomProgress(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildParticleBackground() {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: ParticlePainter(_particleAnimation.value),
          size: Size.infinite,
        );
      },
    );
  }

  Widget _buildMainContent(AppState appState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // AI头像/图标
          _buildAIAvatar(),
          
          const SizedBox(height: 40),
          
          // 处理状态文本
          _buildStatusText(),
          
          const SizedBox(height: 60),
          
          // 用户照片预览
          _buildUserPhotoPreview(appState),
          
          const SizedBox(height: 20),
          
          // 箭头指向
          _buildArrowIndicator(),
          
          const SizedBox(height: 20),
          
          // 选中的服装预览
          _buildClothingPreview(appState),
        ],
      ),
    );
  }

  Widget _buildAIAvatar() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.cyan.withValues(alpha: 0.8),
                  Colors.blue.withValues(alpha: 0.6),
                  Colors.purple.withValues(alpha: 0.4),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.cyan.withValues(alpha: 0.5),
                  blurRadius: 30,
                  spreadRadius: 10,
                ),
              ],
            ),
            child: const Icon(
              Icons.smart_toy,
              color: Colors.white,
              size: 60,
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusText() {
    return Column(
      children: [
        Text(
          'AI 试衣处理中',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.95),
            fontSize: 32,
            fontWeight: FontWeight.w700,
            letterSpacing: 2.0,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          '正在为您生成专属试衣效果...',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 18,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }

  Widget _buildUserPhotoPreview(AppState appState) {
    return Container(
      width: 100,
      height: 130,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(13),
        child: appState.capturedPhotoBytes != null
            ? Image.memory(
                appState.capturedPhotoBytes!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.white.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.person,
                      color: Colors.white.withValues(alpha: 0.5),
                      size: 50,
                    ),
                  );
                },
              )
            : appState.capturedPhoto != null
                ? Image.file(
                    appState.capturedPhoto!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.white.withValues(alpha: 0.1),
                        child: Icon(
                          Icons.person,
                          color: Colors.white.withValues(alpha: 0.5),
                          size: 50,
                        ),
                      );
                    },
                  )
            : Container(
                color: Colors.white.withValues(alpha: 0.1),
                child: Icon(
                  Icons.person,
                  color: Colors.white.withValues(alpha: 0.5),
                  size: 50,
                ),
              ),
      ),
    );
  }

  Widget _buildArrowIndicator() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.arrow_forward,
                color: Colors.cyan.withValues(alpha: 0.8),
                size: 30,
              ),
              const SizedBox(width: 10),
              Icon(
                Icons.auto_awesome,
                color: Colors.purple.withValues(alpha: 0.8),
                size: 25,
              ),
              const SizedBox(width: 10),
              Icon(
                Icons.arrow_forward,
                color: Colors.cyan.withValues(alpha: 0.8),
                size: 30,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildClothingPreview(AppState appState) {
    return Container(
      width: 100,
      height: 130,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(13),
        child: appState.selectedClothing?.imagePath.isNotEmpty == true
            ? Image.asset(
                appState.selectedClothing!.imagePath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.white.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.checkroom,
                      color: Colors.white.withValues(alpha: 0.5),
                      size: 50,
                    ),
                  );
                },
              )
            : Container(
                color: Colors.white.withValues(alpha: 0.1),
                child: Icon(
                  Icons.checkroom,
                  color: Colors.white.withValues(alpha: 0.5),
                  size: 50,
                ),
              ),
      ),
    );
  }

  Widget _buildScanLineEffect() {
    return AnimatedBuilder(
      animation: _scanAnimation,
      builder: (context, child) {
        return Positioned(
          top: MediaQuery.of(context).size.height * _scanAnimation.value - 2,
          left: 0,
          right: 0,
          child: Container(
            height: 4,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Colors.cyan.withValues(alpha: 0.8),
                  Colors.transparent,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.cyan.withValues(alpha: 0.6),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopStatusBar() {
    return Positioned(
      top: 60,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.cyan.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.cyan,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'AI 处理中',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            
            Text(
              TimeOfDay.now().format(context),
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomProgress() {
    return Positioned(
      bottom: 80,
      left: 0,
      right: 0,
      child: Column(
        children: [
          // 进度文本
          Text(
            '预计还需 3 秒...',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // 进度条
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 60),
            height: 6,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(3),
            ),
            child: AnimatedBuilder(
              animation: _scanAnimation,
              builder: (context, child) {
                return FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _scanAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.cyan.withValues(alpha: 0.8),
                          Colors.purple.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(3),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.cyan.withValues(alpha: 0.4),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// 粒子效果画笔
class ParticlePainter extends CustomPainter {
  final double animationValue;

  ParticlePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    if (size.width <= 0 || size.height <= 0) return; // 防止无效尺寸
    
    final paint = Paint()
      ..color = Colors.cyan.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    // 绘制粒子 - 简化计算避免错误
    for (int i = 0; i < 30; i++) { // 减少粒子数量
      try {
        final baseX = size.width * (i % 6) / 6;
        final baseY = size.height * (i ~/ 6) / 5;
        
        final offsetX = (animationValue * 15 * ((i % 3) - 1)).clamp(-size.width/2, size.width/2);
        final offsetY = (animationValue * 20 * ((i % 2) == 0 ? 1 : -1)).clamp(-size.height/2, size.height/2);
        
        final x = (baseX + offsetX).clamp(0.0, size.width);
        final y = (baseY + offsetY).clamp(0.0, size.height);
        
        final opacity = (0.3 + 0.4 * ((animationValue + i * 0.1) % 1.0)).clamp(0.0, 0.6);
        paint.color = Colors.cyan.withValues(alpha: opacity);
        
        final radius = (1.5 + (animationValue * 1.5) % 2.0).clamp(1.0, 3.0);
        
        canvas.drawCircle(
          Offset(x, y),
          radius,
          paint,
        );
      } catch (e) {
        // 忽略绘制错误，继续下一个粒子
        continue;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
} 