import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import '../providers/app_state.dart';
import '../models/clothing_item.dart';


class MirrorClothingSelectionScreen extends StatefulWidget {
  const MirrorClothingSelectionScreen({super.key});

  @override
  State<MirrorClothingSelectionScreen> createState() => _MirrorClothingSelectionScreenState();
}

class _MirrorClothingSelectionScreenState extends State<MirrorClothingSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _itemController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  String _selectedCategory = ClothingCategory.tops;
  int _selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _itemController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _itemController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final cameraController = appState.cameraController;
        
        return Scaffold(
          body: Stack(
            children: [
              // 背景相机预览（模糊效果）
              _buildBlurredCameraBackground(cameraController),
              
              // 侧边栏服装选择面板
              _buildClothingSelectionPanel(appState),
              
              // 返回按钮
              _buildBackButton(appState),
              
              // 当前试衣预览
              _buildTryOnPreview(appState),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBlurredCameraBackground(CameraController? controller) {
    if (controller == null || !controller.value.isInitialized) {
      return Container(
        color: const Color(0xFF1A1A1A),
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    return Positioned.fill(
      child: Stack(
        children: [
          Transform.scale(
            scale: 1.2,
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationY(3.14159),
              child: CameraPreview(controller),
            ),
          ),
          // 模糊遮罩
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.4),
              backgroundBlendMode: BlendMode.darken,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClothingSelectionPanel(AppState appState) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Align(
          alignment: Alignment.centerRight,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.4,
            height: MediaQuery.of(context).size.height,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Colors.black.withValues(alpha: 0.1),
                  Colors.black.withValues(alpha: 0.85),
                  Colors.black.withValues(alpha: 0.95),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(30),
                bottomLeft: Radius.circular(30),
              ),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // 标题区域
                _buildPanelHeader(),
                
                // 分类标签
                _buildCategoryTabs(),
                
                // 服装网格
                Expanded(
                  child: _buildClothingGrid(appState),
                ),
                
                // 底部操作按钮
                _buildBottomActions(appState),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPanelHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择服装',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.95),
              fontSize: 28,
              fontWeight: FontWeight.w700,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击试穿你喜欢的款式',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: ClothingCategory.all.map((category) {
          final isSelected = category == _selectedCategory;
          return Expanded(
            child: GestureDetector(
              onTap: () => _selectCategory(category),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(16),
                  border: isSelected
                      ? Border.all(
                          color: Colors.white.withValues(alpha: 0.4),
                          width: 1,
                        )
                      : null,
                ),
                child: Text(
                  ClothingCategory.getDisplayName(category),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected 
                        ? Colors.white 
                        : Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildClothingGrid(AppState appState) {
    final filteredClothing = appState.clothingItems
        .where((item) => item.category == _selectedCategory)
        .toList();

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 0.85, // 调整宽高比，让卡片更合适
        ),
        itemCount: filteredClothing.length,
        itemBuilder: (context, index) {
          final item = filteredClothing[index];
          return _buildClothingItem(item, index, appState);
        },
      ),
    );
  }

  Widget _buildClothingItem(ClothingItem item, int index, AppState appState) {
    final isSelected = _selectedIndex == index;
    
    return GestureDetector(
      onTap: () => _selectClothingItem(item, index, appState),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: isSelected ? 0.15 : 0.08),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? Colors.white.withValues(alpha: 0.5)
                : Colors.white.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 服装图片 - 增大高度
            SizedBox(
              height: 180, // 增大图片区域高度
              child: Container(
                margin: const EdgeInsets.all(8), // 减小边距让图片更大
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: item.imagePath.isNotEmpty
                      ? Image.asset(
                          item.imagePath,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildPlaceholderImage(),
                        )
                      : _buildPlaceholderImage(),
                ),
              ),
            ),
            
            // 服装信息 - 固定高度
            SizedBox(
              height: 60, // 增加文字区域高度
              child: Padding(
                padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      item.name,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (item.description.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Text(
                        item.description,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.6),
                          fontSize: 11,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Icon(
        Icons.checkroom,
        color: Colors.white.withValues(alpha: 0.5),
        size: 40,
      ),
    );
  }

  Widget _buildBottomActions(AppState appState) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 确认试穿按钮
          if (_selectedIndex >= 0)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _confirmSelection(appState),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white.withValues(alpha: 0.9),
                  foregroundColor: const Color(0xFF1A1A1A),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  '确认试穿',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          
          const SizedBox(height: 12),
          
          // 随机推荐按钮
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => _randomRecommendation(appState),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white.withValues(alpha: 0.9),
                side: BorderSide(
                  color: Colors.white.withValues(alpha: 0.4),
                  width: 1.5,
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shuffle,
                    size: 18,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    '随机推荐',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackButton(AppState appState) {
    return Positioned(
      top: 60,
      left: 20,
      child: GestureDetector(
        onTap: () => appState.showCameraScreen(),
        child: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.arrow_back_ios_new,
            color: Colors.white.withValues(alpha: 0.9),
            size: 24,
          ),
        ),
      ),
    );
  }

  Widget _buildTryOnPreview(AppState appState) {
    if (appState.selectedClothing == null) return const SizedBox.shrink();
    
    return Positioned(
      left: 50,
      bottom: 50,
      child: Container(
        width: 120,
        height: 160,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                '试穿预览',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              child: Container(
                margin: const EdgeInsets.fromLTRB(8, 0, 8, 8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(
                  child: Icon(
                    Icons.person,
                    color: Colors.white70,
                    size: 40,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 事件处理方法
  void _selectCategory(String category) {
    setState(() {
      _selectedCategory = category;
      _selectedIndex = -1; // 重置选中项
    });
  }

  void _selectClothingItem(ClothingItem item, int index, AppState appState) {
    setState(() {
      _selectedIndex = index;
    });
    
    // 更新应用状态
    appState.selectClothing(item);
    
    // 播放选择动画
    _itemController.forward().then((_) {
      _itemController.reverse();
    });
  }

  void _confirmSelection(AppState appState) {
    if (appState.selectedClothing != null) {
      // 开始AI处理
      appState.showProcessingScreen();
    }
  }

  void _randomRecommendation(AppState appState) {
    final filteredClothing = appState.clothingItems
        .where((item) => item.category == _selectedCategory)
        .toList();
    
    if (filteredClothing.isNotEmpty) {
      final randomIndex = (filteredClothing.length * 0.5).floor();
      final randomItem = filteredClothing[randomIndex];
      _selectClothingItem(randomItem, randomIndex, appState);
    }
  }
} 