class ClothingItem {
  final String id;
  final String name;
  final String description;
  final String category;
  final String imagePath;
  final bool isSelected;
  final String? brand;
  final double? price;
  final String? currency;
  final List<String>? colors;
  final List<String>? sizes;
  final String? style;
  final String? material;
  final List<ClothingImage>? images;
  final List<String>? tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ClothingItem({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.imagePath,
    this.isSelected = false,
    this.brand,
    this.price,
    this.currency,
    this.colors,
    this.sizes,
    this.style,
    this.material,
    this.images,
    this.tags,
    this.createdAt,
    this.updatedAt,
  });

  ClothingItem copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    String? imagePath,
    bool? isSelected,
    String? brand,
    double? price,
    String? currency,
    List<String>? colors,
    List<String>? sizes,
    String? style,
    String? material,
    List<ClothingImage>? images,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ClothingItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      imagePath: imagePath ?? this.imagePath,
      isSelected: isSelected ?? this.isSelected,
      brand: brand ?? this.brand,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      colors: colors ?? this.colors,
      sizes: sizes ?? this.sizes,
      style: style ?? this.style,
      material: material ?? this.material,
      images: images ?? this.images,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory ClothingItem.fromJson(Map<String, dynamic> json) {
    return ClothingItem(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      category: json['category'],
      imagePath: json['images']?.isNotEmpty == true 
          ? json['images'][0]['url'] 
          : json['image_path'] ?? '',
      brand: json['brand'],
      price: json['price']?.toDouble(),
      currency: json['currency'],
      colors: json['colors'] != null ? List<String>.from(json['colors']) : null,
      sizes: json['sizes'] != null ? List<String>.from(json['sizes']) : null,
      style: json['style'],
      material: json['material'],
      images: json['images'] != null 
          ? (json['images'] as List).map((img) => ClothingImage.fromJson(img)).toList()
          : null,
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'image_path': imagePath,
      'brand': brand,
      'price': price,
      'currency': currency,
      'colors': colors,
      'sizes': sizes,
      'style': style,
      'material': material,
      'images': images?.map((img) => img.toJson()).toList(),
      'tags': tags,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class ClothingImage {
  final String url;
  final String type;
  final String? alt;

  ClothingImage({
    required this.url,
    required this.type,
    this.alt,
  });

  factory ClothingImage.fromJson(Map<String, dynamic> json) {
    return ClothingImage(
      url: json['url'],
      type: json['type'],
      alt: json['alt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'type': type,
      'alt': alt,
    };
  }
}

class ClothingCategory {
  static const String tops = 'tops';
  static const String dresses = 'dresses';
  static const String outerwear = 'outerwear';

  static const List<String> all = [tops, dresses, outerwear];

  static String getDisplayName(String category) {
    switch (category) {
      case tops:
        return '上衣';
      case dresses:
        return '连衣裙';
      case outerwear:
        return '外套';
      default:
        return category;
    }
  }
}