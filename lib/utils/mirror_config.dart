import 'package:flutter/material.dart';

class MirrorConfig {
  // 镜子模式设置
  static const bool enableMirrorMode = true;
  static const bool autoSwitchBasedOnAspectRatio = false; // 改为false，总是使用镜子模式
  static const double mirrorModeAspectRatioThreshold = 1.5;
  
  // 手势识别设置
  static const double swipeThreshold = 50.0;
  static const int doubleTapTimeLimit = 300; // 毫秒
  static const double longPressThreshold = 500; // 毫秒
  
  // 动画设置
  static const int defaultAnimationDuration = 300; // 毫秒
  static const int breathingAnimationDuration = 2000; // 毫秒
  static const int fadeAnimationDuration = 800; // 毫秒
  
  // UI透明度设置
  static const double overlayOpacity = 0.15;
  static const double borderOpacity = 0.3;
  static const double textOpacity = 0.9;
  static const double hintOpacity = 0.7;
  
  // 自动隐藏设置
  static const int controlsHideDelay = 3000; // 毫秒
  static const int helpTextHideDelay = 5000; // 毫秒
  
  // 相机设置
  static const double cameraScaleForFullscreen = 1.2;
  static const bool enableMirrorFlip = true; // 镜像翻转
  
  // 服装选择面板设置
  static const double clothingPanelWidthRatio = 0.4; // 相对于屏幕宽度
  static const int clothingGridCrossAxisCount = 2;
  static const double clothingItemAspectRatio = 0.75;
  
  // 颜色主题
  static const int primaryColorValue = 0xFF1A1A1A;
  static const int accentColorValue = 0xFF00BCD4; // 青色
  static const int successColorValue = 0xFF4CAF50; // 绿色
  static const int warningColorValue = 0xFFFF9800; // 橙色
  static const int errorColorValue = 0xFFF44336; // 红色
  
  // 特效设置
  static const bool enableParticleEffect = true;
  static const bool enableShimmerEffect = true;
  static const bool enableScanLineEffect = true;
  static const int particleCount = 50;
  
  // 音效设置（如果需要）
  static const bool enableSoundEffects = false;
  static const bool enableHapticFeedback = true;
  
  // 性能设置
  static const bool enableHighQualityAnimations = true;
  static const int maxImageResolution = 1920; // 最大图片分辨率
  
  // 镜子装饰效果
  static const bool enableMirrorFrame = true;
  static const double mirrorFrameWidth = 2.0;
  static const bool enableReflectionEffect = true;
  
  // 获取是否应该启用镜子模式
  static bool shouldEnableMirrorMode(double aspectRatio) {
    if (!enableMirrorMode) return false;
    if (!autoSwitchBasedOnAspectRatio) return enableMirrorMode;
    return aspectRatio > mirrorModeAspectRatioThreshold;
  }
  
  // 获取动画持续时间
  static Duration getAnimationDuration(AnimationType type) {
    switch (type) {
      case AnimationType.defaultAnimation:
        return Duration(milliseconds: defaultAnimationDuration);
      case AnimationType.breathing:
        return Duration(milliseconds: breathingAnimationDuration);
      case AnimationType.fade:
        return Duration(milliseconds: fadeAnimationDuration);
    }
  }
  
  // 获取延迟时间
  static Duration getDelay(DelayType type) {
    switch (type) {
      case DelayType.controlsHide:
        return Duration(milliseconds: controlsHideDelay);
      case DelayType.helpTextHide:
        return Duration(milliseconds: helpTextHideDelay);
    }
  }
}

enum AnimationType {
  defaultAnimation,
  breathing,
  fade,
}

enum DelayType {
  controlsHide,
  helpTextHide,
}

// 镜子模式的主题扩展
class MirrorTheme {
  static const Color primaryColor = Color(MirrorConfig.primaryColorValue);
  static const Color accentColor = Color(MirrorConfig.accentColorValue);
  static const Color successColor = Color(MirrorConfig.successColorValue);
  static const Color warningColor = Color(MirrorConfig.warningColorValue);
  static const Color errorColor = Color(MirrorConfig.errorColorValue);
  
  // 获取带透明度的颜色
  static Color getOverlayColor(double opacity) {
    return Colors.white.withValues(alpha: opacity * MirrorConfig.overlayOpacity);
  }
  
  static Color getBorderColor(double opacity) {
    return Colors.white.withValues(alpha: opacity * MirrorConfig.borderOpacity);
  }
  
  static Color getTextColor(double opacity) {
    return Colors.white.withValues(alpha: opacity * MirrorConfig.textOpacity);
  }
  
  static Color getHintColor(double opacity) {
    return Colors.white.withValues(alpha: opacity * MirrorConfig.hintOpacity);
  }
}